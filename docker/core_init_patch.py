"""
Patched version of core/__init__.py that delays LLM imports to avoid OpenTelemetry issues.
"""

# Import settings immediately as it doesn't cause OpenTelemetry issues
from core.settings import settings

# Delay the LLM import until it's actually needed
_get_model = None

def get_model(*args, **kwargs):
    """Lazy import and call get_model to avoid OpenTelemetry issues at startup."""
    global _get_model
    if _get_model is None:
        from core.llm import get_model as _get_model_impl
        _get_model = _get_model_impl
    return _get_model(*args, **kwargs)

__all__ = ["settings", "get_model"]
