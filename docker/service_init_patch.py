"""
Patched version of service/__init__.py that delays service imports to avoid OpenTelemetry issues.
"""

# Delay the service import until it's actually needed
_app = None

def get_app():
    """Lazy import and return the app to avoid OpenTelemetry issues at startup."""
    global _app
    if _app is None:
        from service.service import app as _app_impl
        _app = _app_impl
    return _app

# Create a proxy object that behaves like the app but delays import
class AppProxy:
    def __getattr__(self, name):
        app = get_app()
        return getattr(app, name)
    
    def __call__(self, *args, **kwargs):
        app = get_app()
        return app(*args, **kwargs)

app = AppProxy()

__all__ = ["app"]
