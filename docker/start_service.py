#!/usr/bin/env python3
"""
Startup script for the service that handles potential import and configuration issues.
"""
import os
import sys
import time

# Fix OpenTelemetry context loading issues BEFORE any other imports
os.environ['OTEL_PYTHON_CONTEXT'] = 'contextvars_context'
os.environ['OTEL_PYTHON_DISABLED'] = 'true'

# Disable OpenTelemetry auto-instrumentation completely
os.environ['OTEL_SDK_DISABLED'] = 'true'
os.environ['OTEL_PYTHON_LOGGING_AUTO_INSTRUMENTATION_ENABLED'] = 'false'

# Disable <PERSON><PERSON>mith tracing which is causing the OpenTelemetry import issues
os.environ['LANGCHAIN_TRACING_V2'] = 'false'
os.environ['LANGSMITH_TRACING'] = 'false'

# Monkey patch OpenTelemetry before any imports
def patch_opentelemetry():
    """Aggressively patch OpenTelemetry to prevent context loading issues."""
    import types
    import contextvars

    # Create comprehensive OpenTelemetry mocks

    # Mock Context class
    class MockContext:
        def __init__(self, *args, **kwargs):
            pass
        def get(self, *args, **kwargs):
            return None
        def set(self, *args, **kwargs):
            return self

    # Mock context.context module
    mock_context_context_module = types.ModuleType('opentelemetry.context.context')
    mock_context_context_module.Context = MockContext

    # Mock main context module
    mock_context_module = types.ModuleType('opentelemetry.context')
    mock_context_module._RUNTIME_CONTEXT = contextvars
    mock_context_module._load_runtime_context = lambda: contextvars
    mock_context_module.context = mock_context_context_module

    # Mock trace module
    mock_trace_module = types.ModuleType('opentelemetry.trace')

    class MockTracerProvider:
        def __init__(self, *args, **kwargs):
            pass
        def get_tracer(self, *args, **kwargs):
            return MockTracer()

    class MockTracer:
        def start_span(self, *args, **kwargs):
            return MockSpan()
        def __enter__(self):
            return self
        def __exit__(self, *args):
            pass

    class MockSpan:
        def __enter__(self):
            return self
        def __exit__(self, *args):
            pass
        def set_attribute(self, *args, **kwargs):
            pass
        def set_status(self, *args, **kwargs):
            pass
        def record_exception(self, *args, **kwargs):
            pass

    # Mock trace functions
    def mock_get_tracer(*args, **kwargs):
        return MockTracer()

    def mock_set_tracer_provider(*args, **kwargs):
        pass

    mock_trace_module.get_tracer = mock_get_tracer
    mock_trace_module.set_tracer_provider = mock_set_tracer_provider
    mock_trace_module.TracerProvider = MockTracerProvider
    mock_trace_module.Tracer = MockTracer
    mock_trace_module.Span = MockSpan

    # Add more classes that are expected in the main trace module
    class MockLink:
        def __init__(self, *args, **kwargs):
            pass

    class MockSpanKind:
        INTERNAL = 1
        SERVER = 2
        CLIENT = 3
        PRODUCER = 4
        CONSUMER = 5

    mock_trace_module.Link = MockLink
    mock_trace_module.SpanKind = MockSpanKind

    # Mock trace.span submodule
    mock_trace_span_module = types.ModuleType('opentelemetry.trace.span')

    class MockSpanContext:
        def __init__(self, *args, **kwargs):
            pass

    class MockStatus:
        def __init__(self, *args, **kwargs):
            pass

    class MockTraceState:
        def __init__(self, *args, **kwargs):
            pass

    mock_trace_span_module.SpanContext = MockSpanContext
    mock_trace_span_module.Status = MockStatus
    mock_trace_span_module.TraceState = MockTraceState
    mock_trace_module.span = mock_trace_span_module

    # Mock SDK modules
    mock_sdk_module = types.ModuleType('opentelemetry.sdk')
    mock_sdk_trace_module = types.ModuleType('opentelemetry.sdk.trace')
    mock_sdk_trace_module.TracerProvider = MockTracerProvider
    mock_sdk_module.trace = mock_sdk_trace_module

    # Mock SDK trace export module
    mock_sdk_trace_export_module = types.ModuleType('opentelemetry.sdk.trace.export')

    class MockSpanExporter:
        def __init__(self, *args, **kwargs):
            pass
        def export(self, *args, **kwargs):
            return None
        def shutdown(self, *args, **kwargs):
            return None

    class MockBatchSpanProcessor:
        def __init__(self, *args, **kwargs):
            pass
        def shutdown(self, *args, **kwargs):
            return None

    mock_sdk_trace_export_module.SpanExporter = MockSpanExporter
    mock_sdk_trace_export_module.BatchSpanProcessor = MockBatchSpanProcessor
    mock_sdk_trace_module.export = mock_sdk_trace_export_module

    # Mock SDK resources module
    mock_sdk_resources_module = types.ModuleType('opentelemetry.sdk.resources')

    class MockResource:
        def __init__(self, *args, **kwargs):
            pass
        def merge(self, *args, **kwargs):
            return self

    mock_sdk_resources_module.Resource = MockResource
    mock_sdk_resources_module.SERVICE_NAME = "service.name"
    mock_sdk_module.resources = mock_sdk_resources_module

    # Also add Resource to the trace module since some imports expect it there
    mock_sdk_trace_module.Resource = MockResource

    # Add more classes that are expected in the trace module
    class MockEvent:
        def __init__(self, *args, **kwargs):
            pass

    class MockReadableSpan:
        def __init__(self, *args, **kwargs):
            pass

    mock_sdk_trace_module.Event = MockEvent
    mock_sdk_trace_module.ReadableSpan = MockReadableSpan

    # Mock other potential SDK modules
    mock_sdk_metrics_module = types.ModuleType('opentelemetry.sdk.metrics')
    mock_sdk_module.metrics = mock_sdk_metrics_module

    # Mock SDK metrics export module
    mock_sdk_metrics_export_module = types.ModuleType('opentelemetry.sdk.metrics.export')

    class MockMetricsData:
        def __init__(self, *args, **kwargs):
            pass

    mock_sdk_metrics_export_module.MetricsData = MockMetricsData
    mock_sdk_metrics_module.export = mock_sdk_metrics_export_module

    # Mock SDK environment_variables module with all possible OTLP environment variables
    mock_sdk_env_vars_module = types.ModuleType('opentelemetry.sdk.environment_variables')

    # Add all common OTLP environment variables
    otlp_env_vars = [
        "OTEL_EXPORTER_OTLP_ENDPOINT",
        "OTEL_EXPORTER_OTLP_HEADERS",
        "OTEL_EXPORTER_OTLP_TIMEOUT",
        "OTEL_EXPORTER_OTLP_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_CLIENT_KEY",
        "OTEL_EXPORTER_OTLP_PRIVATE_KEY",
        "OTEL_EXPORTER_OTLP_PRIVATE_KEY_PASSWORD",
        "OTEL_EXPORTER_OTLP_COMPRESSION",
        "OTEL_EXPORTER_OTLP_INSECURE",
        "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT",
        "OTEL_EXPORTER_OTLP_TRACES_HEADERS",
        "OTEL_EXPORTER_OTLP_TRACES_TIMEOUT",
        "OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY",
        "OTEL_EXPORTER_OTLP_TRACES_COMPRESSION",
        "OTEL_EXPORTER_OTLP_TRACES_INSECURE",
        "OTEL_EXPORTER_OTLP_METRICS_ENDPOINT",
        "OTEL_EXPORTER_OTLP_METRICS_HEADERS",
        "OTEL_EXPORTER_OTLP_METRICS_TIMEOUT",
        "OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY",
        "OTEL_EXPORTER_OTLP_METRICS_COMPRESSION",
        "OTEL_EXPORTER_OTLP_METRICS_INSECURE",
        "OTEL_EXPORTER_OTLP_LOGS_ENDPOINT",
        "OTEL_EXPORTER_OTLP_LOGS_HEADERS",
        "OTEL_EXPORTER_OTLP_LOGS_TIMEOUT",
        "OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE",
        "OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY",
        "OTEL_EXPORTER_OTLP_LOGS_COMPRESSION",
        "OTEL_EXPORTER_OTLP_LOGS_INSECURE",
    ]

    # Set all environment variables as string constants
    for var in otlp_env_vars:
        setattr(mock_sdk_env_vars_module, var, var)

    mock_sdk_module.environment_variables = mock_sdk_env_vars_module

    # Mock SDK util module
    mock_sdk_util_module = types.ModuleType('opentelemetry.sdk.util')
    mock_sdk_util_instrumentation_module = types.ModuleType('opentelemetry.sdk.util.instrumentation')

    class MockInstrumentationScope:
        def __init__(self, *args, **kwargs):
            pass

    mock_sdk_util_instrumentation_module.InstrumentationScope = MockInstrumentationScope
    mock_sdk_util_module.instrumentation = mock_sdk_util_instrumentation_module
    mock_sdk_module.util = mock_sdk_util_module

    # Install all the mocks in sys.modules
    sys.modules['opentelemetry.context'] = mock_context_module
    sys.modules['opentelemetry.context.context'] = mock_context_context_module
    sys.modules['opentelemetry.trace'] = mock_trace_module
    sys.modules['opentelemetry.trace.span'] = mock_trace_span_module
    sys.modules['opentelemetry.sdk'] = mock_sdk_module
    sys.modules['opentelemetry.sdk.trace'] = mock_sdk_trace_module
    sys.modules['opentelemetry.sdk.trace.export'] = mock_sdk_trace_export_module
    sys.modules['opentelemetry.sdk.resources'] = mock_sdk_resources_module
    sys.modules['opentelemetry.sdk.metrics'] = mock_sdk_metrics_module
    sys.modules['opentelemetry.sdk.environment_variables'] = mock_sdk_env_vars_module
    sys.modules['opentelemetry.sdk.util'] = mock_sdk_util_module
    sys.modules['opentelemetry.sdk.util.instrumentation'] = mock_sdk_util_instrumentation_module

# Apply the patch immediately
patch_opentelemetry()

import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if required environment variables are set."""
    logger.info("Checking environment configuration...")

    # Check if at least one LLM provider is configured
    providers = [
        'OPENAI_API_KEY',
        'ANTHROPIC_API_KEY',
        'GOOGLE_API_KEY',
        'GROQ_API_KEY',
        'USE_FAKE_MODEL',
        'OPENROUTER_API_KEY'
    ]

    configured_providers = []
    for provider in providers:
        if os.getenv(provider):
            if provider == 'USE_FAKE_MODEL' and os.getenv(provider).lower() == 'true':
                configured_providers.append('FAKE_MODEL')
            elif provider != 'USE_FAKE_MODEL':
                configured_providers.append(provider)

    if not configured_providers:
        logger.warning("No LLM providers configured. Setting USE_FAKE_MODEL=true as fallback.")
        os.environ['USE_FAKE_MODEL'] = 'true'
    else:
        logger.info(f"Configured providers: {configured_providers}")

    # Set default database configuration if not set
    if not os.getenv('DATABASE_TYPE'):
        os.environ['DATABASE_TYPE'] = 'sqlite'
        logger.info("Set DATABASE_TYPE to sqlite (default)")

    if not os.getenv('SQLITE_DB_PATH'):
        os.environ['SQLITE_DB_PATH'] = '/app/checkpoints.db'
        logger.info("Set SQLITE_DB_PATH to /app/checkpoints.db (default)")



def start_service():
    """Start the main service."""
    logger.info("Starting the service...")

    try:
        # Import and run the main service
        import asyncio
        import uvicorn
        from dotenv import load_dotenv

        # Load environment variables
        load_dotenv()

        # Import settings after environment is configured
        from core import settings

        logger.info(f"Service will start on {settings.HOST}:{settings.PORT}")

        # Set the compatible event loop policy on Windows Systems
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        # Import the app after all environment setup is complete
        logger.info("Importing service app...")
        from service import app

        # Start the service with the imported app
        uvicorn.run(
            app,
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.is_dev()
        )

    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)



def main():
    """Main startup function."""
    logger.info("=== Starting CodePlus Platform Service ===")

    # Wait a moment for container to fully initialize
    time.sleep(1)

    try:
        # Check and configure environment
        check_environment()

        # Start the service
        start_service()

    except KeyboardInterrupt:
        logger.info("Service stopped by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)

if __name__ == "__main__":
    main()
