# Assumptions:

* Team size: 6 members (mix of FE, BE, AI/ML, Full Stack)
* Capacity per dev per week: \~32 hours (accounting for meetings, testing, review)
* Total capacity per sprint (Phase 1): 6 devs × 32 hrs = 192 hrs/sprint
* Goal for Phase 1: Implement at least 50% of core functionality (frontend + backend + agent flow MVP)

# 📊 Progress Tracking

**Total Progress: 0/41 tasks completed (0%)**

- **Not Started:** 41 tasks
- **In Progress:** 0 tasks  
- **Completed:** 0 tasks

—

# 📌 Phase 1: 3 Weeks (3 Sprints) – MVP

Goal: Build working pipeline from input → skill sync → roadmap → report (first version)

### 🚀 Sprint 1 (Week 1): Core Input + Skill Sync Flow

| #   | Task Group       | Task                                             | Est. Hours | Assigned To  | PIC                           | Status      | Step in Pipeline       |
| --- | ---------------- | ------------------------------------------------ | ---------- | ------------ | ----------------------------- | ----------- | ---------------------- |
| 1   | Project Setup    | Setup Frontend scaffolding                       | 3          | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 2   | Project Setup    | Setup Backend scaffolding                        | 3          | Backend Dev  | TruongPH2                     | Not Started |                        |
| 3   | Project Setup    | Setup AI Service scaffolding                     | 3          | AI/ML Dev    | NamNH46                       | Done        |                        |
| 4   | Project Setup    | Setup CI/CD Pipeline                             | 2          | DevOps       | NamNH46                       | Not Started |                        |
| 5   | Research         | Research about RAG                               | 8          | AI/ML Dev    | PhongTN                       | Not Started |                        |
| 6   | Research         | Research about UI/UX                             | 8          | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 7   | Research         | Research about agent gap analysis, agent roadmap | 8          | AI/ML Dev    | QuyetDB, TruongPH2, TrungDD22 | Not Started |                        |
| 8   | Prototype        | Create prototype for all pages and main scenario | 12         | Frontend Dev | DaiNQ11                       | Not Started |                        |
| 9   | Authentication   | SSO Integration Backend (FSOFT mocked)           | 8          | Backend Dev  | TruongPH2                     | Not Started | User Authentication    |
| 10  | Authentication   | SSO Integration Frontend (UI)                    | 4          | Frontend Dev | DaiNQ11                       | Not Started | User Authentication    |
| 11  | Input Collection | Create Learning Request Form (FE)                | 10         | Frontend Dev | DaiNQ11                       | Not Started | Input Collection       |
| 12  | Input Collection | Backend API: Submit Learning Request             | 10         | Backend Dev  | TruongPH2                     | Not Started | Input Collection       |
| 13  | Skill Sync       | Skill Sync Engine: Connect to mock data          | 16         | Backend Dev  | PhongTN                       | Not Started | Skill Profile Fetching |
| 14  | Skill Sync       | Skill Profile Viewer (FE)                        | 10         | Frontend Dev | DaiNQ11                       | Not Started | Skill Profile Display  |
| 15  | Data Models      | DB Models: Learning Request, Skill Profile       | 6          | Backend Dev  | TruongPH2                     | Not Started |                        |

🕒 Sprint Total: \~99 hrs total (includes research phase)

---

### 🚀 Sprint 2 (Week 2): Gap Analysis + Roadmap Generation (Basic)

| #   | Task Group      | Task                                             | Est. Hours | Assigned To  | PIC       | Status      | Step in Pipeline     |
| --- | --------------- | ------------------------------------------------ | ---------- | ------------ | --------- | ----------- | -------------------- |
| 15  | Gap Analysis    | Build Gap Analysis Engine (Backend Logic)        | 10         | Backend Dev  | TruongPH2 | Not Started | Gap Analysis         |
| 16  | Gap Analysis    | Build Gap Analysis Engine (AI Logic)             | 8          | AI/ML Dev    | QuyetDB   | Not Started | Gap Analysis         |
| 17  | Gap Analysis    | Build Gap Analysis Engine (Frontend UI)          | 8          | Frontend Dev | DaiNQ11   | Not Started | Gap Analysis Display |
| 18  | Target Profile  | Build RAG-based Target Profile Parser            | 12         | AI/ML Dev    | PhongTN   | Not Started | Target Profile Gen   |
| 19  | Gap Analysis    | Store & view Gap Results (Backend)               | 4          | Backend Dev  | TruongPH2 | Not Started | Gap Analysis Display |
| 20  | Gap Analysis    | Store & view Gap Results (Frontend)              | 4          | Frontend Dev | DaiNQ11   | Not Started | Gap Analysis Display |
| 21  | Roadmap         | Learning Roadmap Generator (AI Logic)            | 10         | AI/ML Dev    | QuyetDB   | Not Started | Roadmap Generation   |
| 22  | Roadmap         | Learning Roadmap Generator (Backend API)         | 8          | Backend Dev  | TruongPH2 | Not Started | Roadmap Generation   |
| 23  | Roadmap         | Display Roadmap Viewer (timeline basic)          | 12         | Frontend Dev | DaiNQ11   | Not Started | Roadmap Display      |
| 24  | Data Models     | DB Models: Gap Analysis, Target Profile, Roadmap | 6          | Backend Dev  | TruongPH2 | Not Started |                      |
| 25  | Interaction Log | Agent Interaction Log (basic)                    | 8          | Backend Dev  | TruongPH2 | Not Started |                      |
| 26  | API Development | API: Fetch Gap/Roadmap                           | 6          | Backend Dev  | TruongPH2 | Not Started |                      |

🕒 Sprint Total: \~90 hrs AI/BE, \~76 hrs FE = 166 hrs

---

### 🚀 Sprint 3 (Week 3): Report Generator + Edits + Export

| #   | Task Group         | Task                                    | Est. Hours | Assigned To   | PIC       | Status      | Step in Pipeline       |
| --- | ------------------ | --------------------------------------- | ---------- | ------------- | --------- | ----------- | ---------------------- |
| 27  | Report Generation  | Agent Report Generator (Markdown)       | 10         | AI/ML Dev     | QuyetDB   | Not Started | Report Generation      |
| 28  | Report Generation  | Report Viewer (FE)                      | 8          | Frontend Dev  | DaiNQ11   | Not Started | Report Display         |
| 29  | Report Generation  | Export to PDF (Backend Logic)           | 4          | Backend Dev   | TruongPH2 | Not Started | Report Export          |
| 30  | Report Generation  | Export to PDF (Frontend UI)             | 2          | Frontend Dev  | DaiNQ11   | Not Started | Report Export          |
| 31  | Report Editing     | Agent Report Editor Backend             | 6          | Backend Dev   | TruongPH2 | Not Started | Report Editing         |
| 32  | Report Editing     | Agent Report Editor Frontend            | 4          | Frontend Dev  | DaiNQ11   | Not Started | Report Editing         |
| 33  | Roadmap Editing    | Roadmap Edit UI                         | 10         | Frontend Dev  | DaiNQ11   | Not Started | Roadmap Editing        |
| 34  | Skill Sync         | Skill Sync Scheduler (weekly/manual)    | 8          | Backend Dev   | PhongTN   | Not Started | Skill Profile Fetching |
| 35  | Version Management | Sync Skill Change Trigger → New Version | 6          | Backend Dev   | PhongTN   | Not Started | Version Management     |
| 36  | User Communication | Notification for roadmap version        | 4          | Backend Dev   | PhongTN   | Not Started | User Communication     |
| 37  | Log Display        | Interaction Log Viewer                  | 6          | Frontend Dev  | DaiNQ11   | Not Started | Log Display            |
| 38  | Quality Assurance  | Unit + Integration Testing              | 8          | All           | All       | Not Started |                        |
| 39  | Quality Assurance  | Refactor SonarQube issues               | 6          | All           | All       | Not Started |                        |
| 40  | Quality Assurance  | Unit test coverage                      | 8          | All (rotated) | All       | Not Started |                        |
| 41  | Quality Assurance  | Final QA & Bug Fix Round                | 8          | All           | All       | Not Started |                        |

🕒 Sprint Total: \~90 hrs (includes testing and QA)