# Techstack

- [MindsDB](https://www.mindsdb.com/) - RAG
- LLMs API:
  - [OpenRouter](https://openrouter.ai/)
  - [OpenAI](https://platform.openai.com/)
  - [<PERSON>](https://www.anthropic.com/)
  - [<PERSON>](https://ai.google.dev/gemini)
- Agent framework: Langgraph

# Design

## Main UI/UX Style

The application using AI Agent chat based UI/UX style, which is similar to ChatGPT, but with more features and a more complex flow.

## Techstack

- [React](https://react.dev/) - Frontend
- [Node.js](https://nodejs.org/en) - Backend
- [Express.js](https://expressjs.com/) - Backend Framework
- [Postgresql](https://www.postgresql.org/) - Database

### Backend project structure

### Frontend project structure
