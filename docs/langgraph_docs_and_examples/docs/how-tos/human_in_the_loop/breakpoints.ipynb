{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "ed746260-7027-4399-bbab-a0cdb1e53ec2", "metadata": {}, "source": ["# Use breakpoints\n", "\n", "## Requirements\n", "\n", "To use breakpoints, you will need to:\n", "\n", "1. [**Specify a checkpointer**](../../../concepts/persistence#checkpoints) to save the graph state after each step.\n", "2. [**Set breakpoints**](#setting-breakpoints) to specify where execution should pause.\n", "3. **Run the graph** with a [**thread ID**](../../../concepts/persistence#threads) to pause execution at the breakpoint.\n", "4. **Resume execution** using `invoke`/`ainvoke`/`stream`/`astream` passing a `None` as the argument for the inputs.\n", "\n", "## Setting breakpoints\n", "\n", "There are two places where you can set breakpoints:\n", "\n", "1. **Before** or **after** a node executes by setting breakpoints at **compile time** or **run time**. We call these [**static breakpoints**](#static-breakpoints).\n", "2. **Inside** a node using the `NodeInterrupt` exception. We call these [**dynamic breakpoints**](#dynamic-breakpoints).\n", "\n", "## Static breakpoints\n", "\n", "Static breakpoints are triggered either **before** or **after** a node executes. You can set static breakpoints by specifying `interrupt_before` and `interrupt_after` at **\"compile\" time** or **run time**.\n", "\n", "Static breakpoints can be especially useful for debugging if you want to step through the graph execution one\n", "node at a time or if you want to pause the graph execution at specific nodes.\n", "\n", "=== \"Compile time\"\n", "\n", "    ```python\n", "    # highlight-next-line\n", "    graph = graph_builder.compile( # (1)!\n", "        # highlight-next-line\n", "        interrupt_before=[\"node_a\"], # (2)!\n", "        # highlight-next-line\n", "        interrupt_after=[\"node_b\", \"node_c\"], # (3)!\n", "        checkpointer=checkpointer, # (4)!\n", "    )\n", "\n", "    config = {\n", "        \"configurable\": {\n", "            \"thread_id\": \"some_thread\"\n", "        }\n", "    }\n", "\n", "    # Run the graph until the breakpoint\n", "    graph.invoke(inputs, config=thread_config) # (5)!\n", "\n", "    # Resume the graph\n", "    graph.invoke(None, config=thread_config) # (6)!\n", "    ```\n", "\n", "    1. The breakpoints are set during `compile` time.\n", "    2. `interrupt_before` specifies the nodes where execution should pause before the node is executed.\n", "    3. `interrupt_after` specifies the nodes where execution should pause after the node is executed.\n", "    4. A checkpointer is required to enable breakpoints.\n", "    5. The graph is run until the first breakpoint is hit.\n", "    6. The graph is resumed by passing in `None` for the input. This will run the graph until the next breakpoint is hit.\n", "\n", "=== \"Run time\"\n", "\n", "    ```python\n", "    # highlight-next-line\n", "    graph.invoke( # (1)!\n", "        inputs, \n", "        # highlight-next-line\n", "        interrupt_before=[\"node_a\"], # (2)!\n", "        # highlight-next-line\n", "        interrupt_after=[\"node_b\", \"node_c\"] # (3)!\n", "        config={\n", "            \"configurable\": {\"thread_id\": \"some_thread\"}\n", "        }, \n", "    )\n", "\n", "    config = {\n", "        \"configurable\": {\n", "            \"thread_id\": \"some_thread\"\n", "        }\n", "    }\n", "\n", "    # Run the graph until the breakpoint\n", "    graph.invoke(inputs, config=config) # (4)!\n", "\n", "    # Resume the graph\n", "    graph.invoke(None, config=config) # (5)!\n", "    ```\n", "\n", "    1. `graph.invoke` is called with the `interrupt_before` and `interrupt_after` parameters. This is a run-time configuration and can be changed for every invocation.\n", "    2. `interrupt_before` specifies the nodes where execution should pause before the node is executed.\n", "    3. `interrupt_after` specifies the nodes where execution should pause after the node is executed.\n", "    4. The graph is run until the first breakpoint is hit.\n", "    5. The graph is resumed by passing in `None` for the input. This will run the graph until the next breakpoint is hit.\n", "\n", "    !!! note\n", "\n", "        You cannot set static breakpoints at runtime for **sub-graphs**.\n", "        If you have a sub-graph, you must set the breakpoints at compilation time.\n", "\n", "??? example \"Setting static breakpoints\"\n", "\n", "    ```python\n", "    from IPython.display import Image, display\n", "    from typing_extensions import TypedDict\n", "    \n", "    from langgraph.checkpoint.memory import InMemorySaver \n", "    from langgraph.graph import StateGraph, START, END\n", "    \n", "    \n", "    class State(TypedDict):\n", "        input: str\n", "    \n", "    \n", "    def step_1(state):\n", "        print(\"---Step 1---\")\n", "        pass\n", "    \n", "    \n", "    def step_2(state):\n", "        print(\"---Step 2---\")\n", "        pass\n", "    \n", "    \n", "    def step_3(state):\n", "        print(\"---Step 3---\")\n", "        pass\n", "    \n", "    \n", "    builder = StateGraph(State)\n", "    builder.add_node(\"step_1\", step_1)\n", "    builder.add_node(\"step_2\", step_2)\n", "    builder.add_node(\"step_3\", step_3)\n", "    builder.add_edge(START, \"step_1\")\n", "    builder.add_edge(\"step_1\", \"step_2\")\n", "    builder.add_edge(\"step_2\", \"step_3\")\n", "    builder.add_edge(\"step_3\", END)\n", "    \n", "    # Set up a checkpointer \n", "    checkpointer = InMemorySaver() # (1)!\n", "    \n", "    graph = builder.compile(\n", "        checkpointer=checkpointer, # (2)!\n", "        interrupt_before=[\"step_3\"] # (3)!\n", "    )\n", "    \n", "    # View\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "    \n", "    \n", "    # Input\n", "    initial_input = {\"input\": \"hello world\"}\n", "    \n", "    # Thread\n", "    thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "    \n", "    # Run the graph until the first interruption\n", "    for event in graph.stream(initial_input, thread, stream_mode=\"values\"):\n", "        print(event)\n", "        \n", "    # This will run until the breakpoint\n", "    # You can get the state of the graph at this point\n", "    print(graph.get_state(config))\n", "    \n", "    # You can continue the graph execution by passing in `None` for the input\n", "    for event in graph.stream(None, thread, stream_mode=\"values\"):\n", "        print(event)\n", "    ```"]}, {"attachments": {}, "cell_type": "markdown", "id": "b7d5f6a5-9e59-43e4-a4b6-8ada6dace691", "metadata": {}, "source": ["## Dynamic breakpoints\n", "\n", "Use dynamic breakpoints if you need to interrupt the graph from inside a given node based on a condition.\n", "\n", "```python\n", "from langgraph.errors import NodeInterrupt\n", "\n", "def step_2(state: State) -> State:\n", "    # highlight-next-line\n", "    if len(state[\"input\"]) > 5:\n", "        # highlight-next-line\n", "        raise NodeInterrupt( # (1)!\n", "            f\"Received input that is longer than 5 characters: {state['foo']}\"\n", "        )\n", "    return state\n", "```\n", "\n", "1. raise NodeInterrupt exception based on a some condition. In this example, we create a dynamic breakpoint if the length of the attribute `input` is longer than 5 characters.\n", "\n", "<details class=\"example\"><summary>Using dynamic breakpoints</summary>"]}, {"cell_type": "code", "execution_count": 16, "id": "9a14c8b2-5c25-4201-93ea-e5358ee99bcb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict\n", "from IPython.display import Image, display\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.errors import NodeInterrupt\n", "\n", "\n", "class State(TypedDict):\n", "    input: str\n", "\n", "\n", "def step_1(state: State) -> State:\n", "    print(\"---Step 1---\")\n", "    return state\n", "\n", "\n", "def step_2(state: State) -> State:\n", "    # Let's optionally raise a NodeInterrupt\n", "    # if the length of the input is longer than 5 characters\n", "    if len(state[\"input\"]) > 5:\n", "        raise NodeInterrupt(\n", "            f\"Received input that is longer than 5 characters: {state['input']}\"\n", "        )\n", "    print(\"---Step 2---\")\n", "    return state\n", "\n", "\n", "def step_3(state: State) -> State:\n", "    print(\"---Step 3---\")\n", "    return state\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"step_2\", step_2)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"step_2\")\n", "builder.add_edge(\"step_2\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Compile the graph with memory\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"attachments": {}, "cell_type": "markdown", "id": "ef321c80-2677-4bb7-86a4-d2bd2439b4fb", "metadata": {}, "source": ["First, let's run the graph with an input that <= 5 characters long. This should safely ignore the interrupt condition we defined and return the original input at the end of the graph execution."]}, {"cell_type": "code", "execution_count": 17, "id": "b2d281f1-3349-4378-8918-7665fa7a7457", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hello'}\n", "---Step 1---\n", "{'input': 'hello'}\n", "---Step 2---\n", "{'input': 'hello'}\n", "---Step 3---\n", "{'input': 'hello'}\n"]}], "source": ["initial_input = {\"input\": \"hello\"}\n", "thread_config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "for event in graph.stream(initial_input, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "2b66b926-47eb-401b-b37b-d80269d7214c", "metadata": {}, "source": ["If we inspect the graph at this point, we can see that there are no more tasks left to run and that the graph indeed finished execution."]}, {"cell_type": "code", "execution_count": 18, "id": "4eac1455-e7ef-4a32-8c14-0d5789409689", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["()\n", "()\n"]}], "source": ["state = graph.get_state(thread_config)\n", "print(state.next)\n", "print(state.tasks)"]}, {"attachments": {}, "cell_type": "markdown", "id": "f8e03817-2135-4fb3-b881-fd6d2c378ccf", "metadata": {}, "source": ["Now, let's run the graph with an input that's longer than 5 characters. This should trigger the dynamic interrupt we defined via raising a `NodeInterrupt` error inside the `step_2` node."]}, {"cell_type": "code", "execution_count": 19, "id": "c06192ad-13a4-4d2e-8e30-f1c08578fe77", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hello world'}\n", "---Step 1---\n", "{'input': 'hello world'}\n", "{'__interrupt__': (Interrupt(value='Received input that is longer than 5 characters: hello world', resumable=False, ns=None),)}\n"]}], "source": ["initial_input = {\"input\": \"hello world\"}\n", "thread_config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "173fd4f1-db97-44bb-a9e5-435ed042e3a3", "metadata": {}, "source": ["We can see that the graph now stopped while executing `step_2`. If we inspect the graph state at this point, we can see the information on what node is set to execute next (`step_2`), as well as what node raised the interrupt (also `step_2`), and additional information about the interrupt."]}, {"cell_type": "code", "execution_count": 20, "id": "2058593c-178e-4a23-a4c4-860d4a9c2198", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('step_2',)\n", "(PregelTask(id='bfc767e3-a6c4-c5af-dbbf-0d20ea64501e', name='step_2', path=('__pregel_pull', 'step_2'), error=None, interrupts=(Interrupt(value='Received input that is longer than 5 characters: hello world', resumable=False, ns=None),), state=None, result=None),)\n"]}], "source": ["state = graph.get_state(thread_config)\n", "print(state.next)\n", "print(state.tasks)"]}, {"cell_type": "markdown", "id": "fc36d1be-ae2e-49c8-a17f-2b27be09618a", "metadata": {}, "source": ["If we try to resume the graph from the breakpoint, we will simply interrupt again as our inputs & graph state haven't changed."]}, {"cell_type": "code", "execution_count": 21, "id": "872e7a69-9784-4f81-90c6-6b6af2fa6480", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hello world'}\n", "{'__interrupt__': (Interrupt(value='Received input that is longer than 5 characters: hello world', resumable=False, ns=None),)}\n"]}], "source": ["# NOTE: to resume the graph from a dynamic interrupt we use the same syntax as with regular interrupts -- we pass None as the input\n", "for event in graph.stream(None, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "code", "execution_count": 24, "id": "3275f899-7039-4029-8814-0bb5c33fabfe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('step_2',)\n", "(PregelTask(id='bfc767e3-a6c4-c5af-dbbf-0d20ea64501e', name='step_2', path=('__pregel_pull', 'step_2'), error=None, interrupts=(Interrupt(value='Received input that is longer than 5 characters: hello world', resumable=False, ns=None),), state=None, result=None),)\n"]}], "source": ["state = graph.get_state(thread_config)\n", "print(state.next)\n", "print(state.tasks)"]}, {"cell_type": "markdown", "id": "f2000f6c-029e-4c0a-8db5-7504dc185d01", "metadata": {}, "source": ["</details>"]}, {"cell_type": "markdown", "id": "4eb3ebb1-cf26-4082-977e-796eadb6f654", "metadata": {}, "source": ["## Use with subgraphs\n", "\n", "To add breakpoints to subgraph either:\n", "\n", "* Define [static breakpoints](#static-breakpoints) by specifying them when **compiling** the subgraph.\n", "* Define [dynamic breakpoints](#dynamic-breakpoints).\n", "\n", "\n", "<details class=\"example\"><summary>Add breakpoints to subgraphs</summary>"]}, {"cell_type": "code", "execution_count": 21, "id": "3c3edfe3-4633-46d2-9723-db7f7be28830", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["StateSnapshot(values={'foo': ''}, next=('subgraph_node_1',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': 'node_1:dfc321bb-7c91-ccfe-23b8-c2374ae3f1cc', 'checkpoint_id': '1f02a8d1-985a-6e2c-8000-77034088c0ce', 'checkpoint_map': {'': '1f02a8d1-9856-6264-8000-ed1534455427', 'node_1:dfc321bb-7c91-ccfe-23b8-c2374ae3f1cc': '1f02a8d1-985a-6e2c-8000-77034088c0ce'}}}, metadata={'source': 'loop', 'writes': None, 'step': 0, 'parents': {'': '1f02a8d1-9856-6264-8000-ed1534455427'}, 'thread_id': '1', 'langgraph_step': 1, 'langgraph_node': 'node_1', 'langgraph_triggers': ['branch:to:node_1'], 'langgraph_path': ['__pregel_pull', 'node_1'], 'langgraph_checkpoint_ns': 'node_1:dfc321bb-7c91-ccfe-23b8-c2374ae3f1cc'}, created_at='2025-05-06T15:16:35.543192+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': 'node_1:dfc321bb-7c91-ccfe-23b8-c2374ae3f1cc', 'checkpoint_id': '1f02a8d1-9859-6d41-bfff-872b2e8f4db6', 'checkpoint_map': {'': '1f02a8d1-9856-6264-8000-ed1534455427', 'node_1:dfc321bb-7c91-ccfe-23b8-c2374ae3f1cc': '1f02a8d1-9859-6d41-bfff-872b2e8f4db6'}}}, tasks=(PregelTask(id='33218e09-8747-5161-12b1-5dc705d30b51', name='subgraph_node_1', path=('__pregel_pull', 'subgraph_node_1'), error=None, interrupts=(), state=None, result=None),), interrupts=())\n"]}, {"data": {"text/plain": ["{'foo': ''}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import START, StateGraph\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from langgraph.types import interrupt\n", "\n", "\n", "class State(TypedDict):\n", "    foo: str\n", "\n", "\n", "def subgraph_node_1(state: State):\n", "    return {\"foo\": state[\"foo\"]}\n", "\n", "\n", "subgraph_builder = StateGraph(State)\n", "subgraph_builder.add_node(subgraph_node_1)\n", "subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "\n", "subgraph = subgraph_builder.compile(interrupt_before=[\"subgraph_node_1\"])\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"node_1\", subgraph)  # directly include subgraph as a node\n", "builder.add_edge(START, \"node_1\")\n", "\n", "checkpointer = InMemorySaver()\n", "graph = builder.compile(checkpointer=checkpointer)\n", "\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "graph.invoke({\"foo\": \"\"}, config)\n", "\n", "# Fetch state including subgraph state.\n", "print(graph.get_state(config, subgraphs=True).tasks[0].state)\n", "\n", "# resume the subgraph\n", "graph.invoke(None, config)"]}, {"cell_type": "markdown", "id": "e509da54-8805-4a54-a07c-1b7805fa6b95", "metadata": {}, "source": ["</details>"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}