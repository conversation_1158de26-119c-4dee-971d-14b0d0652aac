{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "c23e0a59-759a-4226-997e-8a69f7661d32", "metadata": {}, "source": ["# Use time-travel\n", "\n", "To use time-travel in LangGraph:\n", "\n", "\n", "1. **Run the graph** with initial inputs using `invoke` or `stream` APIs.\n", "2. **Identify a checkpoint in an existing thread**: Use the [`get_state_history()`][langgraph.graph.graph.CompiledGraph.get_state_history] method to retrieve the execution history for a specific `thread_id` and locate the desired `checkpoint_id`.  \n", "   Alternatively, set a [breakpoint](../../../concepts/breakpoints/) before the node(s) where you want execution to pause. You can then find the most recent checkpoint recorded up to that breakpoint.\n", "3. **(Optional) modify the graph state**: Use the [`update_state`][langgraph.graph.graph.CompiledGraph.update_state] method to modify the graph’s state at the checkpoint and resume execution from alternative state.\n", "4. **Resume execution from the checkpoint**: Use the `invoke` or `stream` APIs with an input of `None` and a configuration containing the appropriate `thread_id` and `checkpoint_id`.\n", "\n", "## Example\n", "\n", "This example builds a simple LangGraph workflow that generates a joke topic and writes a joke using an LLM. It demonstrates how to run the graph, retrieve past execution checkpoints, optionally modify the state, and resume execution from a chosen checkpoint to explore alternate outcomes.\n", "\n", "### Setup\n", "\n", "First we need to install the packages required"]}, {"cell_type": "code", "execution_count": 38, "id": "af4ce0ba-7596-4e5f-8bf8-0b0bd6e62833", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_anthropic"]}, {"cell_type": "markdown", "id": "0abe11f4-62ed-4dc4-8875-3db21e260d1d", "metadata": {}, "source": ["Next, we need to set API keys for Anthropic (the LLM we will use)"]}, {"cell_type": "code", "execution_count": 1, "id": "c903a1cf-2977-4e2d-ad7d-8b3946821d89", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "f0ed46a8-effe-4596-b0e1-a6a29ee16f5c", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 2, "id": "4e1e9b07-4185-4d6c-8834-a0ae07dfdf4b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x7694f1dd7ed0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import uuid\n", "\n", "from typing_extensions import TypedDict, NotRequired\n", "from langgraph.graph import StateGraph, START, END\n", "from langchain.chat_models import init_chat_model\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "\n", "class State(TypedDict):\n", "    topic: NotRequired[str]\n", "    joke: NotRequired[str]\n", "\n", "\n", "llm = init_chat_model(\n", "    \"anthropic:claude-3-7-sonnet-latest\",\n", "    temperature=0,\n", ")\n", "\n", "\n", "def generate_topic(state: State):\n", "    \"\"\"LLM call to generate a topic for the joke\"\"\"\n", "    msg = llm.invoke(\"Give me a funny topic for a joke\")\n", "    return {\"topic\": msg.content}\n", "\n", "\n", "def write_joke(state: State):\n", "    \"\"\"LLM call to write a joke based on the topic\"\"\"\n", "    msg = llm.invoke(f\"Write a short joke about {state['topic']}\")\n", "    return {\"joke\": msg.content}\n", "\n", "\n", "# Build workflow\n", "workflow = StateGraph(State)\n", "\n", "# Add nodes\n", "workflow.add_node(\"generate_topic\", generate_topic)\n", "workflow.add_node(\"write_joke\", write_joke)\n", "\n", "# Add edges to connect nodes\n", "workflow.add_edge(START, \"generate_topic\")\n", "workflow.add_edge(\"generate_topic\", \"write_joke\")\n", "workflow.add_edge(\"write_joke\", END)\n", "\n", "# Compile\n", "checkpointer = InMemorySaver()\n", "graph = workflow.compile(checkpointer=checkpointer)\n", "graph"]}, {"cell_type": "markdown", "id": "7ef76675-02e4-42ef-b707-0a9ce36ba0ac", "metadata": {}, "source": ["### 1. Run the graph"]}, {"cell_type": "code", "execution_count": 3, "id": "3199a8d0-17c0-4979-ac08-52d7007b7689", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["How about \"The Secret Life of Socks in the Dryer\"? You know, exploring the mysterious phenomenon of how socks go into the laundry as pairs but come out as singles. Where do they go? Are they starting new lives elsewhere? Is there a sock paradise we don't know about? There's a lot of comedic potential in the everyday mystery that unites us all!\n", "\n", "# The Secret Life of Socks in the Dryer\n", "\n", "I finally discovered where all my missing socks go after the dryer. Turns out they're not missing at all—they've just eloped with someone else's socks from the laundromat to start new lives together.\n", "\n", "My blue argyle is now living in Bermuda with a red polka dot, posting vacation photos on Sockstagram and sending me lint as alimony.\n"]}], "source": ["config = {\n", "    \"configurable\": {\n", "        \"thread_id\": uuid.uuid4(),\n", "    }\n", "}\n", "state = graph.invoke({}, config)\n", "\n", "print(state[\"topic\"])\n", "print()\n", "print(state[\"joke\"])"]}, {"cell_type": "markdown", "id": "2fc0fc7f-5a7b-4b48-b1e2-d413ad4a51aa", "metadata": {}, "source": ["### 2. Identify a checkpoint"]}, {"cell_type": "code", "execution_count": 4, "id": "e7f54f90-bff1-4810-8488-c8fce6b5a03f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["()\n", "1f02ac4a-ec9f-6524-8002-8f7b0bbeed0e\n", "\n", "('write_joke',)\n", "1f02ac4a-ce2a-6494-8001-cb2e2d651227\n", "\n", "('generate_topic',)\n", "1f02ac4a-a4e0-630d-8000-b73c254ba748\n", "\n", "('__start__',)\n", "1f02ac4a-a4dd-665e-bfff-e6c8c44315d9\n", "\n"]}], "source": ["# The states are returned in reverse chronological order.\n", "states = list(graph.get_state_history(config))\n", "\n", "for state in states:\n", "    print(state.next)\n", "    print(state.config[\"configurable\"][\"checkpoint_id\"])\n", "    print()"]}, {"cell_type": "code", "execution_count": 5, "id": "2f5d918d-0c1b-42b8-9d26-15b58aaaf058", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('write_joke',)\n", "{'topic': 'How about \"The Secret Life of Socks in the Dryer\"? You know, exploring the mysterious phenomenon of how socks go into the laundry as pairs but come out as singles. Where do they go? Are they starting new lives elsewhere? Is there a sock paradise we don\\'t know about? There\\'s a lot of comedic potential in the everyday mystery that unites us all!'}\n"]}], "source": ["# This is the state before last (states are listed in chronological order)\n", "selected_state = states[1]\n", "print(selected_state.next)\n", "print(selected_state.values)"]}, {"cell_type": "markdown", "id": "59d5ecb8-1291-4131-83b6-c666862a09fc", "metadata": {}, "source": ["### 3. Update the state (optional)\n", "\n", "`update_state` will create a new checkpoint. The new checkpoint will be associated with the same thread, but a new checkpoint ID."]}, {"cell_type": "code", "execution_count": 6, "id": "5ad78ce6-9a7f-4110-9e4a-8105f21cb90d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'configurable': {'thread_id': 'c62e2e03-c27b-4cb6-8cea-ea9bfedae006', 'checkpoint_ns': '', 'checkpoint_id': '1f02ac4a-ecee-600b-8002-a1d21df32e4c'}}\n"]}], "source": ["new_config = graph.update_state(selected_state.config, values={\"topic\": \"chickens\"})\n", "print(new_config)"]}, {"cell_type": "markdown", "id": "db25cd11-7907-4c6f-9c97-b6b3458f246c", "metadata": {}, "source": ["### 4. Resume execution from the checkpoint"]}, {"cell_type": "code", "execution_count": 7, "id": "f0bab935-6d9b-4cfd-afe8-bfcf4638847e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'chickens',\n", " 'joke': 'Why did the chicken join a band?\\n\\nBecause it had excellent drumsticks!'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke(None, new_config)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}