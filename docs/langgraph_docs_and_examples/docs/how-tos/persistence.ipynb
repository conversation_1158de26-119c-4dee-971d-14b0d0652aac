{"cells": [{"cell_type": "markdown", "id": "51466c8d-8ce4-4b3d-be4e-18fdbeda5f53", "metadata": {}, "source": ["# Add persistence\n", "\n", "Many AI applications need memory to share context across multiple interactions. LangGraph supports two types of memory essential for building conversational agents:\n", "\n", "- **[Short-term memory](#add-short-term-memory)**: Tracks the ongoing conversation by maintaining message history within a session.\n", "- **[Long-term memory](#add-long-term-memory)**: Stores user-specific or application-level data across sessions.\n", "\n", "> **Terminology**\n", ">\n", ">    In LangGraph:\n", ">\n", ">    - *Short-term memory* is also referred to as **thread-level memory**.\n", ">    - *Long-term memory* is also called **cross-thread memory**.\n", ">\n", ">    A [thread](../../concepts/persistence#threads) represents a sequence of related runs\n", ">    grouped by the same `thread_id`."]}, {"cell_type": "code", "execution_count": 1, "id": "af4ce0ba-7596-4e5f-8bf8-0b0bd6e62833", "metadata": {}, "outputs": [], "source": ["# hide-cell\n", "%pip install --quiet -U langgraph \"langchain[anthropic]\""]}, {"cell_type": "code", "execution_count": 2, "id": "c903a1cf-2977-4e2d-ad7d-8b3946821d89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ANTHROPIC_API_KEY:  ········\n"]}], "source": ["# hide-cell\n", "import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "702f9da1-9aaf-4a5f-9b1f-6ab1a273e6a9", "metadata": {}, "source": ["## Add short-term memory"]}, {"cell_type": "markdown", "id": "f7c171e2-82d2-423f-8eba-ff32d7c494fe", "metadata": {}, "source": ["**Short-term** memory (thread-level persistence) enables agents to track multi-turn conversations. To add short-term memory:"]}, {"cell_type": "code", "execution_count": 18, "id": "07584f6a-7b8e-4f18-a135-e0435797e274", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! I'm bob\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON>! How are you doing today? Is there anything I can help you with?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "what's my name?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Your name is <PERSON>.\n"]}], "source": ["from langchain.chat_models import init_chat_model\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "\n", "# highlight-next-line\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "\n", "\n", "def call_model(state: MessagesState):\n", "    response = model.invoke(state[\"messages\"])\n", "    return {\"messages\": response}\n", "\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(call_model)\n", "builder.add_edge(START, \"call_model\")\n", "\n", "checkpointer = InMemorySaver()\n", "# highlight-next-line\n", "graph = builder.compile(checkpointer=checkpointer)\n", "\n", "config = {\n", "    \"configurable\": {\n", "        # highlight-next-line\n", "        \"thread_id\": \"1\"\n", "    }\n", "}\n", "\n", "for chunk in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "    # highlight-next-line\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    chunk[\"messages\"][-1].pretty_print()\n", "\n", "for chunk in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "    # highlight-next-line\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "504bf7c3-40f2-4b7f-86d9-390973a1700c", "metadata": {}, "source": ["!!! info \"Not needed for LangGraph API users\"\n", "\n", "    If you're using the LangGraph API, **don't need** to provide checkpointer when compiling the graph. The API automatically handles checkpointing for you."]}, {"attachments": {}, "cell_type": "markdown", "id": "988a98c0-15a6-492b-aa59-261fab3a4909", "metadata": {}, "source": ["### Use in production\n", "\n", "In production, you would want to use a checkpointer backed by a database:\n", "\n", "```python\n", "from langgraph.checkpoint.postgres import PostgresSaver\n", "\n", "DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "# highlight-next-line\n", "with PostgresSaver.from_conn_string(DB_URI) as checkpointer:\n", "    builder = StateGraph(...)\n", "    # highlight-next-line\n", "    graph = builder.compile(checkpointer=checkpointer)\n", "```\n", "\n", "??? example \"Example: using [Postgres](https://pypi.org/project/langgraph-checkpoint-postgres/) checkpointer\"\n", "\n", "    ```\n", "    pip install -U \"psycopg[binary,pool]\" langgraph langgraph-checkpoint-postgres\n", "    ```\n", "\n", "    !!! Setup\n", "        You need to call `checkpointer.setup()` the first time you're using Postgres checkpointer\n", "\n", "    === \"Sync\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.postgres import PostgresSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "        # highlight-next-line\n", "        with PostgresSaver.from_conn_string(DB_URI) as checkpointer:\n", "            # checkpointer.setup()\n", "        \n", "            def call_model(state: MessagesState):\n", "                response = model.invoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    === \"Async\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "        # highlight-next-line\n", "        async with AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer:\n", "            # await checkpointer.setup()\n", "        \n", "            async def call_model(state: MessagesState):\n", "                response = await model.ainvoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    \n", "\n", "??? example \"Example: using [MongoDB](https://pypi.org/project/langgraph-checkpoint-mongodb/) checkpointer\"\n", "\n", "    ```\n", "    pip install -U pymongo langgraph langgraph-checkpoint-mongodb\n", "    ```\n", "\n", "    !!! note \"Setup\"\n", "\n", "        To use the MongoDB checkpointer, you will need a MongoDB cluster. Follow [this guide](https://www.mongodb.com/docs/guides/atlas/cluster/) to create a cluster if you don't already have one.\n", "\n", "    === \"Sync\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.mongodb import MongoDBSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"localhost:27017\"\n", "        # highlight-next-line\n", "        with MongoDBSaver.from_conn_string(DB_URI) as checkpointer:\n", "        \n", "            def call_model(state: MessagesState):\n", "                response = model.invoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    === \"Async\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.mongodb.aio import AsyncMongoDBSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"localhost:27017\"\n", "        # highlight-next-line\n", "        async with AsyncMongoDBSaver.from_conn_string(DB_URI) as checkpointer:\n", "        \n", "            async def call_model(state: MessagesState):\n", "                response = await model.ainvoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```    \n", "\n", "??? example \"Example: using [Redis](https://pypi.org/project/langgraph-checkpoint-redis/) checkpointer\"\n", "\n", "    ```\n", "    pip install -U langgraph langgraph-checkpoint-redis\n", "    ```\n", "\n", "    !!! Setup\n", "        You need to call `checkpointer.setup()` the first time you're using Redis checkpointer\n", "\n", "\n", "    === \"Sync\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.redis import RedisSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"redis://localhost:6379\"\n", "        # highlight-next-line\n", "        with RedisSaver.from_conn_string(DB_URI) as checkpointer:\n", "            # checkpointer.setup()\n", "        \n", "            def call_model(state: MessagesState):\n", "                response = model.invoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    === \"Async\"\n", "\n", "        ```python\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        # highlight-next-line\n", "        from langgraph.checkpoint.redis.aio import AsyncRedisSaver\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"redis://localhost:6379\"\n", "        # highlight-next-line\n", "        async with AsyncRedisSaver.from_conn_string(DB_URI) as checkpointer:\n", "            # await checkpointer.asetup()\n", "        \n", "            async def call_model(state: MessagesState):\n", "                response = await model.ainvoke(state[\"messages\"])\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            # highlight-next-line\n", "            graph = builder.compile(checkpointer=checkpointer)\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\"\n", "                }\n", "            }\n", "        \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what's my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\"\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()     \n", "        ```"]}, {"cell_type": "markdown", "id": "53f54d98-c659-49af-ae1e-a25641924ad1", "metadata": {}, "source": ["### Use with subgraphs"]}, {"cell_type": "markdown", "id": "cf3ec9f4-08bc-4118-af7b-1d3c4a5ef69b", "metadata": {}, "source": ["If your graph contains [subgraphs](../../concepts/subgraphs), you only need to **provide the checkpointer when compiling the parent graph**. LangGraph will automatically propagate the checkpointer to the child subgraphs.\n", "\n", "```python\n", "from langgraph.graph import START, StateGraph\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from typing import TypedDict\n", "\n", "class State(TypedDict):\n", "    foo: str\n", "\n", "# Subgraph\n", "\n", "def subgraph_node_1(state: State):\n", "    return {\"foo\": state[\"foo\"] + \"bar\"}\n", "\n", "subgraph_builder = StateGraph(State)\n", "subgraph_builder.add_node(subgraph_node_1)\n", "subgraph_builder.add_edge(START, \"subgraph_node_1\")\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile()\n", "\n", "# Parent graph\n", "\n", "def node_1(state: State):\n", "    return {\"foo\": \"hi! \" + state[\"foo\"]}\n", "\n", "builder = StateGraph(State)\n", "# highlight-next-line\n", "builder.add_node(\"node_1\", subgraph)\n", "builder.add_edge(START, \"node_1\")\n", "\n", "checkpointer = InMemorySaver()\n", "# highlight-next-line\n", "graph = builder.compile(checkpointer=checkpointer)\n", "```    \n", "\n", "If you want the subgraph to have its own memory, you can compile it `with checkpointer=True`. This is useful in [multi-agent](../../concepts/multi_agent) systems, if you want agents to keep track of their internal message histories:\n", "\n", "```python\n", "subgraph_builder = StateGraph(...)\n", "# highlight-next-line\n", "subgraph = subgraph_builder.compile(checkpointer=True)\n", "```"]}, {"cell_type": "markdown", "id": "f17b4493-c578-428f-857c-ef53f7139d53", "metadata": {}, "source": ["### Use with Functional API\n", "\n", "To add short-term memory to a [Functional API](../../concepts/functional_api) LangGraph workflow:\n", "\n", "1. Pass `checkpointer` instance to the [`entrypoint()`][langgraph.func.entrypoint] decorator:\n", "\n", "    ```python\n", "    from langgraph.func import entrypoint\n", "    \n", "    @entrypoint(checkpointer=checkpointer)\n", "    def workflow(inputs)\n", "        ...\n", "    ```\n", "\n", "2. Optionally expose `previous` parameter in the workflow function signature:\n", "\n", "    ```python\n", "    @entrypoint(checkpointer=checkpointer)\n", "    def workflow(\n", "        inputs,\n", "        *,\n", "        # you can optionally specify `previous` in the workflow function signature\n", "        # to access the return value from the workflow as of the last execution\n", "        previous\n", "    ):\n", "        previous = previous or []\n", "        combined_inputs = previous + inputs\n", "        result = do_something(combined_inputs)\n", "        ...\n", "    ```\n", "\n", "3. Optionally choose which values will be returned from the workflow and which will be saved by the checkpointer as `previous`:\n", "\n", "    ```python\n", "    @entrypoint(checkpointer=checkpointer)\n", "    def workflow(inputs, *, previous):\n", "        ...\n", "        result = do_something(...)\n", "        return entrypoint.final(value=result, save=combine(inputs, result))\n", "    ```\n", "\n", "??? example \"Example: add short-term memory to Functional API workflow\"\n", "\n", "    ```python\n", "    from langchain_core.messages import AnyMessage\n", "    from langgraph.graph import add_messages\n", "    from langgraph.func import entrypoint, task\n", "    from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "    # highlight-next-line\n", "    @task\n", "    def call_model(messages: list[AnyMessage]):\n", "        response = model.invoke(messages)\n", "        return response\n", "    \n", "    checkpointer = InMemorySaver()\n", "\n", "    # highlight-next-line\n", "    @entrypoint(checkpointer=checkpointer)\n", "    def workflow(inputs: list[AnyMessage], *, previous: list[AnyMessage]):\n", "        if previous:\n", "            inputs = add_messages(previous, inputs)\n", "    \n", "        response = call_model(inputs).result()\n", "        return entrypoint.final(value=response, save=add_messages(inputs, response))\n", "    \n", "    config = {\n", "        \"configurable\": {\n", "            # highlight-next-line\n", "            \"thread_id\": \"1\"\n", "        }\n", "    }\n", "    \n", "    for chunk in workflow.invoke(\n", "        [{\"role\": \"user\", \"content\": \"hi! I'm bob\"}],\n", "        # highlight-next-line\n", "        config,\n", "        stream_mode=\"values\",\n", "    ):\n", "        chunk.pretty_print()\n", "    \n", "    for chunk in workflow.stream(\n", "        [{\"role\": \"user\", \"content\": \"what's my name?\"}],\n", "        # highlight-next-line\n", "        config,\n", "        stream_mode=\"values\",\n", "    ):\n", "        chunk.pretty_print()\n", "    ```"]}, {"cell_type": "markdown", "id": "6d4bd273-98e6-4330-af93-b79fb7f50115", "metadata": {}, "source": ["### Manage checkpoints\n", "\n", "You can view and delete the information stored by the checkpointer:\n", "\n", "??? \"View thread state (checkpoint)\"\n", "\n", "    === \"Graph/Functional API\"\n", "    \n", "        ```python\n", "        config = {\n", "            \"configurable\": {\n", "                # highlight-next-line\n", "                \"thread_id\": \"1\",\n", "                # optionally provide an ID for a specific checkpoint,\n", "                # otherwise the latest checkpoint is shown\n", "                # highlight-next-line\n", "                # \"checkpoint_id\": \"1f029ca3-1f5b-6704-8004-820c16b69a5a\"\n", "                  \n", "            }\n", "        }\n", "        # highlight-next-line\n", "        graph.get_state(config)\n", "        ```\n", "    \n", "        ```\n", "        StateSnapshot(\n", "            values={'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today?), HumanMessage(content=\"what's my name?\"), AIMessage(content='Your name is <PERSON>.')]}, next=(), \n", "            config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1f5b-6704-8004-820c16b69a5a'}},\n", "            metadata={\n", "                'source': 'loop',\n", "                'writes': {'call_model': {'messages': AIMessage(content='Your name is <PERSON><PERSON>')}},\n", "                'step': 4,\n", "                'parents': {},\n", "                'thread_id': '1'\n", "            },\n", "            created_at='2025-05-05T16:01:24.680462+00:00',\n", "            parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}}, \n", "            tasks=(),\n", "            interrupts=()\n", "        )\n", "        ```\n", "\n", "    === \"Checkpointer API\"\n", "    \n", "        ```python\n", "        config = {\n", "            \"configurable\": {\n", "                # highlight-next-line\n", "                \"thread_id\": \"1\",\n", "                # optionally provide an ID for a specific checkpoint,\n", "                # otherwise the latest checkpoint is shown\n", "                # highlight-next-line\n", "                # \"checkpoint_id\": \"1f029ca3-1f5b-6704-8004-820c16b69a5a\"\n", "                  \n", "            }\n", "        }\n", "        # highlight-next-line\n", "        checkpointer.get_tuple(config)\n", "        ```\n", "\n", "        ```\n", "        CheckpointTuple(\n", "            config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1f5b-6704-8004-820c16b69a5a'}},\n", "            checkpoint={\n", "                'v': 3,\n", "                'ts': '2025-05-05T16:01:24.680462+00:00',\n", "                'id': '1f029ca3-1f5b-6704-8004-820c16b69a5a',\n", "                'channel_versions': {'__start__': '00000000000000000000000000000005.0.5290678567601859', 'messages': '00000000000000000000000000000006.0.3205149138784782', 'branch:to:call_model': '00000000000000000000000000000006.0.14611156755133758'}, 'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000004.0.5736472536395331'}, 'call_model': {'branch:to:call_model': '00000000000000000000000000000005.0.1410174088651449'}},\n", "                'channel_values': {'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today?), HumanMessage(content=\"what's my name?\"), AIMessage(content='Your name is <PERSON>.')]},\n", "            },\n", "            metadata={\n", "                'source': 'loop',\n", "                'writes': {'call_model': {'messages': AIMessage(content='Your name is <PERSON><PERSON>')}},\n", "                'step': 4,\n", "                'parents': {},\n", "                'thread_id': '1'\n", "            },\n", "            parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}},\n", "            pending_writes=[]\n", "        )\n", "        ```\n", "\n", "??? \"View the history of the thread (checkpoints)\"\n", "\n", "    === \"Graph/Functional API\"\n", "\n", "        ```python\n", "        config = {\n", "            \"configurable\": {\n", "                # highlight-next-line\n", "                \"thread_id\": \"1\"\n", "            }\n", "        }\n", "        # highlight-next-line\n", "        list(graph.get_state_history(config))\n", "        ```\n", "    \n", "        ```\n", "        [\n", "            StateSnapshot(\n", "                values={'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), HumanMessage(content=\"what's my name?\"), AIMessage(content='Your name is <PERSON>.')]}, \n", "                next=(), \n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1f5b-6704-8004-820c16b69a5a'}}, \n", "                metadata={'source': 'loop', 'writes': {'call_model': {'messages': AIMessage(content='Your name is <PERSON><PERSON>')}}, 'step': 4, 'parents': {}, 'thread_id': '1'},\n", "                created_at='2025-05-05T16:01:24.680462+00:00',\n", "                parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}},\n", "                tasks=(),\n", "                interrupts=()\n", "            ),\n", "            StateSnapshot(\n", "                values={'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), HumanMessage(content=\"what's my name?\")]}, \n", "                next=('call_model',), \n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}},\n", "                metadata={'source': 'loop', 'writes': None, 'step': 3, 'parents': {}, 'thread_id': '1'},\n", "                created_at='2025-05-05T16:01:23.863421+00:00',\n", "                parent_config={...}\n", "                tasks=(PregelTask(id='8ab4155e-6b15-b885-9ce5-bed69a2c305c', name='call_model', path=('__pregel_pull', 'call_model'), error=None, interrupts=(), state=None, result={'messages': AIMessage(content='Your name is <PERSON>.')}),),\n", "                interrupts=()\n", "            ),\n", "            StateSnapshot(\n", "                values={'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')]}, \n", "                next=('__start__',), \n", "                config={...}, \n", "                metadata={'source': 'input', 'writes': {'__start__': {'messages': [{'role': 'user', 'content': \"what's my name?\"}]}}, 'step': 2, 'parents': {}, 'thread_id': '1'},\n", "                created_at='2025-05-05T16:01:23.863173+00:00',\n", "                parent_config={...}\n", "                tasks=(PregelTask(id='24ba39d6-6db1-4c9b-f4c5-682aeaf38dcd', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'messages': [{'role': 'user', 'content': \"what's my name?\"}]}),),\n", "                interrupts=()\n", "            ),\n", "            StateSnapshot(\n", "                values={'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')]}, \n", "                next=(), \n", "                config={...}, \n", "                metadata={'source': 'loop', 'writes': {'call_model': {'messages': AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')}}, 'step': 1, 'parents': {}, 'thread_id': '1'},\n", "                created_at='2025-05-05T16:01:23.862295+00:00',\n", "                parent_config={...}\n", "                tasks=(),\n", "                interrupts=()\n", "            ),\n", "            StateSnapshot(\n", "                values={'messages': [HumanMessage(content=\"hi! I'm bob\")]}, \n", "                next=('call_model',), \n", "                config={...}, \n", "                metadata={'source': 'loop', 'writes': None, 'step': 0, 'parents': {}, 'thread_id': '1'}, \n", "                created_at='2025-05-05T16:01:22.278960+00:00', \n", "                parent_config={...}\n", "                tasks=(PregelTask(id='8cbd75e0-3720-b056-04f7-71ac805140a0', name='call_model', path=('__pregel_pull', 'call_model'), error=None, interrupts=(), state=None, result={'messages': AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')}),), \n", "                interrupts=()\n", "            ),\n", "            StateSnapshot(\n", "                values={'messages': []}, \n", "                next=('__start__',), \n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-0870-6ce2-bfff-1f3f14c3e565'}},\n", "                metadata={'source': 'input', 'writes': {'__start__': {'messages': [{'role': 'user', 'content': \"hi! I'm bob\"}]}}, 'step': -1, 'parents': {}, 'thread_id': '1'}, \n", "                created_at='2025-05-05T16:01:22.277497+00:00', \n", "                parent_config=None,\n", "                tasks=(PregelTask(id='d458367b-8265-812c-18e2-33001d199ce6', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'messages': [{'role': 'user', 'content': \"hi! I'm bob\"}]}),), \n", "                interrupts=()\n", "            )\n", "        ]       \n", "        ```\n", "\n", "    === \"Checkpointer API\"\n", "\n", "        ```python\n", "        config = {\n", "            \"configurable\": {\n", "                # highlight-next-line\n", "                \"thread_id\": \"1\"\n", "            }\n", "        }\n", "        # highlight-next-line\n", "        list(checkpointer.list(config))\n", "        ```\n", "\n", "        ```\n", "        [\n", "            CheckpointTuple(\n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1f5b-6704-8004-820c16b69a5a'}}, \n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:24.680462+00:00', \n", "                    'id': '1f029ca3-1f5b-6704-8004-820c16b69a5a', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000005.0.5290678567601859', 'messages': '00000000000000000000000000000006.0.3205149138784782', 'branch:to:call_model': '00000000000000000000000000000006.0.14611156755133758'}, \n", "                    'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000004.0.5736472536395331'}, 'call_model': {'branch:to:call_model': '00000000000000000000000000000005.0.1410174088651449'}},\n", "                    'channel_values': {'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), HumanMessage(content=\"what's my name?\"), AIMessage(content='Your name is <PERSON>.')]},\n", "                },\n", "                metadata={'source': 'loop', 'writes': {'call_model': {'messages': AIMessage(content='Your name is <PERSON><PERSON>')}}, 'step': 4, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}}, \n", "                pending_writes=[]\n", "            ),\n", "            CheckpointTuple(\n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-1790-6b0a-8003-baf965b6a38f'}},\n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:23.863421+00:00', \n", "                    'id': '1f029ca3-1790-6b0a-8003-baf965b6a38f', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000005.0.5290678567601859', 'messages': '00000000000000000000000000000005.0.7935064215293443', 'branch:to:call_model': '00000000000000000000000000000005.0.1410174088651449'}, \n", "                    'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000004.0.5736472536395331'}, 'call_model': {'branch:to:call_model': '00000000000000000000000000000002.0.9300422176788571'}}, \n", "                    'channel_values': {'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?'), HumanMessage(content=\"what's my name?\")], 'branch:to:call_model': None}\n", "                }, \n", "                metadata={'source': 'loop', 'writes': None, 'step': 3, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config={...}, \n", "                pending_writes=[('8ab4155e-6b15-b885-9ce5-bed69a2c305c', 'messages', AIMessage(content='Your name is <PERSON><PERSON>'))]\n", "            ),\n", "            CheckpointTuple(\n", "                config={...}, \n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:23.863173+00:00', \n", "                    'id': '1f029ca3-1790-616e-8002-9e021694a0cd', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000004.0.5736472536395331', 'messages': '00000000000000000000000000000003.0.7056767754077798', 'branch:to:call_model': '00000000000000000000000000000003.0.22059023329132854'}, \n", "                    'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000001.0.7040775356287469'}, 'call_model': {'branch:to:call_model': '00000000000000000000000000000002.0.9300422176788571'}}, \n", "                    'channel_values': {'__start__': {'messages': [{'role': 'user', 'content': \"what's my name?\"}]}, 'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')]}\n", "                }, \n", "                metadata={'source': 'input', 'writes': {'__start__': {'messages': [{'role': 'user', 'content': \"what's my name?\"}]}}, 'step': 2, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config={...}, \n", "                pending_writes=[('24ba39d6-6db1-4c9b-f4c5-682aeaf38dcd', 'messages', [{'role': 'user', 'content': \"what's my name?\"}]), ('24ba39d6-6db1-4c9b-f4c5-682aeaf38dcd', 'branch:to:call_model', None)]\n", "            ),\n", "            CheckpointTuple(\n", "                config={...}, \n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:23.862295+00:00', \n", "                    'id': '1f029ca3-178d-6f54-8001-d7b180db0c89', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000002.0.18673090920108737', 'messages': '00000000000000000000000000000003.0.7056767754077798', 'branch:to:call_model': '00000000000000000000000000000003.0.22059023329132854'}, \n", "                    'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000001.0.7040775356287469'}, 'call_model': {'branch:to:call_model': '00000000000000000000000000000002.0.9300422176788571'}}, \n", "                    'channel_values': {'messages': [HumanMessage(content=\"hi! I'm bob\"), AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')]}\n", "                }, \n", "                metadata={'source': 'loop', 'writes': {'call_model': {'messages': AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?')}}, 'step': 1, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config={...}, \n", "                pending_writes=[]\n", "            ),\n", "            CheckpointTuple(\n", "                config={...}, \n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:22.278960+00:00', \n", "                    'id': '1f029ca3-0874-6612-8000-339f2abc83b1', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000002.0.18673090920108737', 'messages': '00000000000000000000000000000002.0.30296526818059655', 'branch:to:call_model': '00000000000000000000000000000002.0.9300422176788571'}, \n", "                    'versions_seen': {'__input__': {}, '__start__': {'__start__': '00000000000000000000000000000001.0.7040775356287469'}}, \n", "                    'channel_values': {'messages': [HumanMessage(content=\"hi! I'm bob\")], 'branch:to:call_model': None}\n", "                }, \n", "                metadata={'source': 'loop', 'writes': None, 'step': 0, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config={...}, \n", "                pending_writes=[('8cbd75e0-3720-b056-04f7-71ac805140a0', 'messages', AIMessage(content='Hi <PERSON>! How are you doing today? Is there anything I can help you with?'))]\n", "            ),\n", "            CheckpointTuple(\n", "                config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f029ca3-0870-6ce2-bfff-1f3f14c3e565'}}, \n", "                checkpoint={\n", "                    'v': 3, \n", "                    'ts': '2025-05-05T16:01:22.277497+00:00', \n", "                    'id': '1f029ca3-0870-6ce2-bfff-1f3f14c3e565', \n", "                    'channel_versions': {'__start__': '00000000000000000000000000000001.0.7040775356287469'}, \n", "                    'versions_seen': {'__input__': {}}, \n", "                    'channel_values': {'__start__': {'messages': [{'role': 'user', 'content': \"hi! I'm bob\"}]}}\n", "                }, \n", "                metadata={'source': 'input', 'writes': {'__start__': {'messages': [{'role': 'user', 'content': \"hi! I'm bob\"}]}}, 'step': -1, 'parents': {}, 'thread_id': '1'}, \n", "                parent_config=None, \n", "                pending_writes=[('d458367b-8265-812c-18e2-33001d199ce6', 'messages', [{'role': 'user', 'content': \"hi! I'm bob\"}]), ('d458367b-8265-812c-18e2-33001d199ce6', 'branch:to:call_model', None)]\n", "            )\n", "        ]\n", "        ```\n", "\n", "\n", "??? \"Delete all checkpoints for a thread\"\n", "\n", "    ```python\n", "    thread_id = \"1\"\n", "    checkpointer.delete_thread(thread_id)\n", "    ```"]}, {"cell_type": "markdown", "id": "799eaf41-1a48-4a31-bc71-1a2a51e92b21", "metadata": {}, "source": ["## Add long-term memory"]}, {"cell_type": "markdown", "id": "303f3ae3-30e3-41d1-900a-c84879b50f86", "metadata": {}, "source": ["Use **long-term** memory (cross-thread persistence) to store user-specific or application-specific data across conversations. This is useful for applications like chatbots, where you want to remember user preferences or other information.\n", "\n", "To use long-term memory, we need to [provide a store][langgraph.store.base.BaseStore] when creating the graph:"]}, {"cell_type": "code", "execution_count": 5, "id": "b88d8ede-ec8c-406c-917d-cda5610679c3", "metadata": {}, "outputs": [], "source": ["import uuid\n", "from typing_extensions import Annotated, TypedDict\n", "\n", "from langchain_core.runnables import RunnableConfig\n", "from langgraph.graph import StateGraph, MessagesState, START\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "# highlight-next-line\n", "from langgraph.store.memory import InMemoryStore\n", "from langgraph.store.base import BaseStore\n", "\n", "model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "\n", "\n", "def call_model(\n", "    state: MessagesState,\n", "    config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "    *,\n", "    # highlight-next-line\n", "    store: BaseStore,  # (1)!\n", "):\n", "    user_id = config[\"configurable\"][\"user_id\"]\n", "    namespace = (\"memories\", user_id)\n", "    # highlight-next-line\n", "    memories = store.search(namespace, query=str(state[\"messages\"][-1].content))\n", "    info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "    system_msg = f\"You are a helpful assistant talking to the user. User info: {info}\"\n", "\n", "    # Store new memories if the user asks the model to remember\n", "    last_message = state[\"messages\"][-1]\n", "    if \"remember\" in last_message.content.lower():\n", "        memory = \"User name is <PERSON>\"\n", "        # highlight-next-line\n", "        store.put(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "\n", "    response = model.invoke(\n", "        [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "    )\n", "    return {\"messages\": response}\n", "\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(call_model)\n", "builder.add_edge(START, \"call_model\")\n", "\n", "checkpointer = InMemorySaver()\n", "store = InMemoryStore()\n", "\n", "graph = builder.compile(\n", "    checkpointer=checkpointer,\n", "    # highlight-next-line\n", "    store=store,\n", ")"]}, {"cell_type": "markdown", "id": "ce33d6ee-5754-4a9d-8246-6ad188a28d94", "metadata": {}, "source": ["1. This is the `store` we compiled the graph with"]}, {"cell_type": "code", "execution_count": 6, "id": "62ce2760-f23a-4ab1-b73c-d2680e20b611", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hi! Remember: my name is <PERSON>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON>! I'll remember that your name is <PERSON>. How are you doing today?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "what is my name?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Your name is <PERSON>.\n"]}], "source": ["config = {\n", "    \"configurable\": {\n", "        # highlight-next-line\n", "        \"thread_id\": \"1\",\n", "        # highlight-next-line\n", "        \"user_id\": \"1\",\n", "    }\n", "}\n", "for chunk in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"Hi! Remember: my name is <PERSON>\"}]},\n", "    # highlight-next-line\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    chunk[\"messages\"][-1].pretty_print()\n", "\n", "config = {\n", "    \"configurable\": {\n", "        # highlight-next-line\n", "        \"thread_id\": \"2\",\n", "        \"user_id\": \"1\",\n", "    }\n", "}\n", "\n", "for chunk in graph.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"what is my name?\"}]},\n", "    # highlight-next-line\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    chunk[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "cb96703d-3ab3-4d87-a6e6-22f4c0273346", "metadata": {}, "source": ["!!! info \"Not needed for LangGraph API users\"\n", "\n", "    If you're using the LangGraph API, **don't need** to provide store when compiling the graph. The API automatically handles storage infrastructure for you."]}, {"cell_type": "markdown", "id": "b585dbb4-bd99-44bb-82e0-477a151556e6", "metadata": {}, "source": ["### Use in production\n", "\n", "In production, you would want to use a checkpointer backed by a database:\n", "\n", "```python\n", "from langgraph.checkpoint.postgres import PostgresSaver\n", "\n", "DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "# highlight-next-line\n", "with PostgresStore.from_conn_string(DB_URI) as store:\n", "    builder = StateGraph(...)\n", "    # highlight-next-line\n", "    graph = builder.compile(store=store)\n", "```\n", "\n", "??? example \"Example: using [Postgres](https://pypi.org/project/langgraph-checkpoint-postgres/) store\"\n", "\n", "    ```\n", "    pip install -U \"psycopg[binary,pool]\" langgraph langgraph-checkpoint-postgres\n", "    ```\n", "\n", "    !!! Setup\n", "        You need to call `store.setup()` the first time you're using Postgres store\n", "\n", "    === \"Sync\"\n", "\n", "        ```python\n", "        from langchain_core.runnables import RunnableConfig\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        from langgraph.checkpoint.postgres import PostgresSaver\n", "        # highlight-next-line\n", "        from langgraph.store.postgres import PostgresStore\n", "        from langgraph.store.base import BaseStore\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "        \n", "        with (\n", "            # highlight-next-line\n", "            PostgresStore.from_conn_string(DB_URI) as store,\n", "            PostgresSaver.from_conn_string(DB_URI) as checkpointer,\n", "        ):\n", "            # store.setup()\n", "            # checkpointer.setup()\n", "        \n", "            def call_model(\n", "                state: MessagesState,\n", "                config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "                *,\n", "                # highlight-next-line\n", "                store: BaseStore,\n", "            ):\n", "                user_id = config[\"configurable\"][\"user_id\"]\n", "                namespace = (\"memories\", user_id)\n", "                # highlight-next-line\n", "                memories = store.search(namespace, query=str(state[\"messages\"][-1].content))\n", "                info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "                system_msg = f\"You are a helpful assistant talking to the user. User info: {info}\"\n", "            \n", "                # Store new memories if the user asks the model to remember\n", "                last_message = state[\"messages\"][-1]\n", "                if \"remember\" in last_message.content.lower():\n", "                    memory = \"User name is <PERSON>\"\n", "                    # highlight-next-line\n", "                    store.put(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "            \n", "                response = model.invoke(\n", "                    [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "                )\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            graph = builder.compile(\n", "                checkpointer=checkpointer,\n", "                # highlight-next-line\n", "                store=store,\n", "            )\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\",\n", "                    # highlight-next-line\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"Hi! Remember: my name is <PERSON>\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"2\",\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "        \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what is my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    === \"Async\"\n", "\n", "        ```python\n", "        from langchain_core.runnables import RunnableConfig\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver\n", "        # highlight-next-line\n", "        from langgraph.store.postgres.aio import AsyncPostgresStore\n", "        from langgraph.store.base import BaseStore\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"postgresql://postgres:postgres@localhost:5442/postgres?sslmode=disable\"\n", "        \n", "        async with (\n", "            # highlight-next-line\n", "            AsyncPostgresStore.from_conn_string(DB_URI) as store,\n", "            AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer,\n", "        ):\n", "            # await store.setup()\n", "            # await checkpointer.setup()\n", "        \n", "            async def call_model(\n", "                state: MessagesState,\n", "                config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "                *,\n", "                # highlight-next-line\n", "                store: BaseStore,\n", "            ):\n", "                user_id = config[\"configurable\"][\"user_id\"]\n", "                namespace = (\"memories\", user_id)\n", "                # highlight-next-line\n", "                memories = await store.asearch(namespace, query=str(state[\"messages\"][-1].content))\n", "                info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "                system_msg = f\"You are a helpful assistant talking to the user. User info: {info}\"\n", "            \n", "                # Store new memories if the user asks the model to remember\n", "                last_message = state[\"messages\"][-1]\n", "                if \"remember\" in last_message.content.lower():\n", "                    memory = \"User name is <PERSON>\"\n", "                    # highlight-next-line\n", "                    await store.aput(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "        \n", "                response = await model.ainvoke(\n", "                    [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "                )\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            graph = builder.compile(\n", "                checkpointer=checkpointer,\n", "                # highlight-next-line\n", "                store=store,\n", "            )\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\",\n", "                    # highlight-next-line\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"Hi! Remember: my name is <PERSON>\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"2\",\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "        \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what is my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "??? example \"Example: using [Redis](https://pypi.org/project/langgraph-checkpoint-redis/) store\"\n", "\n", "    ```\n", "    pip install -U langgraph langgraph-checkpoint-redis\n", "    ```\n", "\n", "    !!! Setup\n", "        You need to call `store.setup()` the first time you're using Redis store\n", "\n", "\n", "    === \"Sync\"\n", "\n", "        ```python\n", "        from langchain_core.runnables import RunnableConfig\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        from langgraph.checkpoint.redis import RedisSaver\n", "        # highlight-next-line\n", "        from langgraph.store.redis import RedisStore\n", "        from langgraph.store.base import BaseStore\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "            \n", "        DB_URI = \"redis://localhost:6379\"\n", "        \n", "        with (\n", "            # highlight-next-line\n", "            RedisStore.from_conn_string(DB_URI) as store,\n", "            RedisSaver.from_conn_string(DB_URI) as checkpointer,\n", "        ):\n", "            store.setup()\n", "            checkpointer.setup()\n", "        \n", "            def call_model(\n", "                state: MessagesState,\n", "                config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "                *,\n", "                # highlight-next-line\n", "                store: BaseStore,\n", "            ):\n", "                user_id = config[\"configurable\"][\"user_id\"]\n", "                namespace = (\"memories\", user_id)\n", "                # highlight-next-line\n", "                memories = store.search(namespace, query=str(state[\"messages\"][-1].content))\n", "                info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "                system_msg = f\"You are a helpful assistant talking to the user. User info: {info}\"\n", "            \n", "                # Store new memories if the user asks the model to remember\n", "                last_message = state[\"messages\"][-1]\n", "                if \"remember\" in last_message.content.lower():\n", "                    memory = \"User name is <PERSON>\"\n", "                    # highlight-next-line\n", "                    store.put(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "            \n", "                response = model.invoke(\n", "                    [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "                )\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            graph = builder.compile(\n", "                checkpointer=checkpointer,\n", "                # highlight-next-line\n", "                store=store,\n", "            )\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\",\n", "                    # highlight-next-line\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"Hi! Remember: my name is <PERSON>\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"2\",\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "        \n", "            for chunk in graph.stream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what is my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "        ```\n", "\n", "    === \"Async\"\n", "\n", "        ```python\n", "        from langchain_core.runnables import RunnableConfig\n", "        from langchain.chat_models import init_chat_model\n", "        from langgraph.graph import StateGraph, MessagesState, START\n", "        from langgraph.checkpoint.redis.aio import AsyncRedisSaver\n", "        # highlight-next-line\n", "        from langgraph.store.redis.aio import AsyncRedisStore\n", "        from langgraph.store.base import BaseStore\n", "        \n", "        model = init_chat_model(model=\"anthropic:claude-3-5-haiku-latest\")\n", "        \n", "        DB_URI = \"redis://localhost:6379\"\n", "        \n", "        async with (\n", "            # highlight-next-line\n", "            AsyncRedisStore.from_conn_string(DB_URI) as store,\n", "            AsyncRedisSaver.from_conn_string(DB_URI) as checkpointer,\n", "        ):\n", "            # await store.setup()\n", "            # await checkpointer.asetup()\n", "        \n", "            async def call_model(\n", "                state: MessagesState,\n", "                config: <PERSON><PERSON><PERSON>Confi<PERSON>,\n", "                *,\n", "                # highlight-next-line\n", "                store: BaseStore,\n", "            ):\n", "                user_id = config[\"configurable\"][\"user_id\"]\n", "                namespace = (\"memories\", user_id)\n", "                # highlight-next-line\n", "                memories = await store.asearch(namespace, query=str(state[\"messages\"][-1].content))\n", "                info = \"\\n\".join([d.value[\"data\"] for d in memories])\n", "                system_msg = f\"You are a helpful assistant talking to the user. User info: {info}\"\n", "            \n", "                # Store new memories if the user asks the model to remember\n", "                last_message = state[\"messages\"][-1]\n", "                if \"remember\" in last_message.content.lower():\n", "                    memory = \"User name is <PERSON>\"\n", "                    # highlight-next-line\n", "                    await store.aput(namespace, str(uuid.uuid4()), {\"data\": memory})\n", "        \n", "                response = await model.ainvoke(\n", "                    [{\"role\": \"system\", \"content\": system_msg}] + state[\"messages\"]\n", "                )\n", "                return {\"messages\": response}\n", "        \n", "            builder = StateGraph(MessagesState)\n", "            builder.add_node(call_model)\n", "            builder.add_edge(START, \"call_model\")\n", "            \n", "            graph = builder.compile(\n", "                checkpointer=checkpointer,\n", "                # highlight-next-line\n", "                store=store,\n", "            )\n", "        \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"1\",\n", "                    # highlight-next-line\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"Hi! Remember: my name is <PERSON>\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()\n", "            \n", "            config = {\n", "                \"configurable\": {\n", "                    # highlight-next-line\n", "                    \"thread_id\": \"2\",\n", "                    \"user_id\": \"1\",\n", "                }\n", "            }\n", "        \n", "            async for chunk in graph.astream(\n", "                {\"messages\": [{\"role\": \"user\", \"content\": \"what is my name?\"}]},\n", "                # highlight-next-line\n", "                config,\n", "                stream_mode=\"values\",\n", "            ):\n", "                chunk[\"messages\"][-1].pretty_print()  \n", "        ```"]}, {"cell_type": "markdown", "id": "f8aff878-e1c1-4592-951a-380b5bec1f63", "metadata": {}, "source": ["### Use semantic search\n", "\n", "You can enable semantic search in your graph's memory store: this lets graph agent search for items in the store by semantic similarity.\n", "\n", "```python\n", "from langchain.embeddings import init_embeddings\n", "from langgraph.store.memory import InMemoryStore\n", "\n", "# Create store with semantic search enabled\n", "embeddings = init_embeddings(\"openai:text-embedding-3-small\")\n", "store = InMemoryStore(\n", "    index={\n", "        \"embed\": embeddings,\n", "        \"dims\": 1536,\n", "    }\n", ")\n", "\n", "store.put((\"user_123\", \"memories\"), \"1\", {\"text\": \"I love pizza\"})\n", "store.put((\"user_123\", \"memories\"), \"2\", {\"text\": \"I am a plumber\"})\n", "\n", "items = store.search(\n", "    (\"user_123\", \"memories\"), query=\"I'm hungry\", limit=1\n", ")\n", "```\n", "\n", "??? example \"Long-term memory with semantic search\"\n", "\n", "    ```python\n", "    from typing import Optional\n", "    \n", "    from langchain.embeddings import init_embeddings\n", "    from langchain.chat_models import init_chat_model\n", "    from langgraph.store.base import BaseStore\n", "    from langgraph.store.memory import InMemoryStore\n", "    from langgraph.graph import START, MessagesState, StateGraph\n", "    \n", "    llm = init_chat_model(\"openai:gpt-4o-mini\")\n", "    \n", "    # Create store with semantic search enabled\n", "    embeddings = init_embeddings(\"openai:text-embedding-3-small\")\n", "    store = InMemoryStore(\n", "        index={\n", "            \"embed\": embeddings,\n", "            \"dims\": 1536,\n", "        }\n", "    )\n", "    \n", "    store.put((\"user_123\", \"memories\"), \"1\", {\"text\": \"I love pizza\"})\n", "    store.put((\"user_123\", \"memories\"), \"2\", {\"text\": \"I am a plumber\"})\n", "    \n", "    def chat(state, *, store: BaseStore):\n", "        # Search based on user's last message\n", "        items = store.search(\n", "            (\"user_123\", \"memories\"), query=state[\"messages\"][-1].content, limit=2\n", "        )\n", "        memories = \"\\n\".join(item.value[\"text\"] for item in items)\n", "        memories = f\"## Memories of user\\n{memories}\" if memories else \"\"\n", "        response = llm.invoke(\n", "            [\n", "                {\"role\": \"system\", \"content\": f\"You are a helpful assistant.\\n{memories}\"},\n", "                *state[\"messages\"],\n", "            ]\n", "        )\n", "        return {\"messages\": [response]}\n", "    \n", "    \n", "    builder = StateGraph(MessagesState)\n", "    builder.add_node(chat)\n", "    builder.add_edge(START, \"chat\")\n", "    graph = builder.compile(store=store)\n", "    \n", "    for message, metadata in graph.stream(\n", "        input={\"messages\": [{\"role\": \"user\", \"content\": \"I'm hungry\"}]},\n", "        stream_mode=\"messages\",\n", "    ):\n", "        print(message.content, end=\"\")\n", "    ```\n", "\n", "See [this guide](../memory/semantic-search/) for more information on how to use semantic search with LangGraph memory store."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}