{"cells": [{"attachments": {"8ee0a8ce-f0a8-4019-b5bf-b20933e40956.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "a3e3ebc4-57af-4fe4-bdd3-36aff67bf276", "metadata": {}, "source": ["# Multi-agent supervisor\n", "\n", "[**Supervisor**](../../../concepts/multi_agent#supervisor) is a multi-agent architecture where **specialized** agents are coordinated by a central **supervisor agent**. The supervisor agent controls all communication flow and task delegation, making decisions about which agent to invoke based on the current context and task requirements.\n", "\n", "In this tutorial, you will build a supervisor system with two agents — a research and a math expert. By the end of the tutorial you will:\n", "\n", "1. Build specialized research and math agents\n", "2. Build a supervisor for orchestrating them with the prebuilt [`langgraph-supervisor`](https://langchain-ai.github.io/langgraph/agents/multi-agent/#supervisor)\n", "3. Build a supervisor from scratch\n", "4. Implement advanced task delegation\n", "\n", "![diagram](attachment:8ee0a8ce-f0a8-4019-b5bf-b20933e40956.png)\n", "\n", "## Setup\n", "\n", "First, let's install required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "0d30b6f7-3bec-4d9f-af50-43dfdc81ae6c", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langgraph-supervisor langchain-tavily \"langchain[openai]\""]}, {"cell_type": "code", "execution_count": null, "id": "c84adef6-51b3-46c5-8490-c2a1ad967769", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"Please provide your {var}\")\n", "\n", "\n", "_set_if_undefined(\"OPENAI_API_KEY\")\n", "_set_if_undefined(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "be85e3ad", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "349a5673-962e-4de9-ac19-70ee426ef433", "metadata": {}, "source": ["## 1. Create worker agents"]}, {"attachments": {}, "cell_type": "markdown", "id": "ad822cf5-56d4-47f7-ad64-d9fd3f57551e", "metadata": {}, "source": ["First, let's create our specialized worker agents — research agent and math agent:\n", "\n", "* Research agent will have access to a web search tool using [Tavily API](https://tavily.com/)\n", "* Math agent will have access to simple math tools (`add`, `multiply`, `divide`)"]}, {"attachments": {}, "cell_type": "markdown", "id": "5e34989a-8989-432e-b6aa-8de91ab83372", "metadata": {}, "source": ["### Research agent\n", "\n", "For web search, we will use `TavilySearch` tool from `langchain-tavily`:"]}, {"cell_type": "code", "execution_count": 3, "id": "0fdc7345-2967-484b-881a-114dfac3f534", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. He gave voice to a diverse coalition of working families in all five boroughs and is leading the fight to bring back New York City’s economy, reduce inequality, improve public safety, and build a stronger, healthier city that delivers for all New Yorkers. Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. NYC is a trademark and service mark of the City of New York.\n"]}], "source": ["from langchain_tavily import TavilySearch\n", "\n", "web_search = TavilySearch(max_results=3)\n", "web_search_results = web_search.invoke(\"who is the mayor of NYC?\")\n", "\n", "print(web_search_results[\"results\"][0][\"content\"])"]}, {"cell_type": "markdown", "id": "cc899eb6-b08e-41b8-b92b-75908c44e8b0", "metadata": {}, "source": ["To create individual worker agents, we will use LangGraph's prebuilt [agent](../../../agents/agents#basic-configuration)."]}, {"cell_type": "code", "execution_count": 4, "id": "15e179e4-1a49-4c76-9235-0ca989eb0d2d", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "research_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[web_search],\n", "    prompt=(\n", "        \"You are a research agent.\\n\\n\"\n", "        \"INSTRUCTIONS:\\n\"\n", "        \"- Assist ONLY with research-related tasks, DO NOT do any math\\n\"\n", "        \"- After you're done with your tasks, respond to the supervisor directly\\n\"\n", "        \"- Respond ONLY with the results of your work, do NOT include ANY other text.\"\n", "    ),\n", "    name=\"research_agent\",\n", ")"]}, {"cell_type": "markdown", "id": "023ae2f2-517b-4611-a0ca-506b8c0b734e", "metadata": {}, "source": ["Let's [run the agent](../../../agents/run_agents) to verify that it behaves as expected. \n", "\n", "\n", "??? note \"We'll use `pretty_print_messages` helper to render the streamed agent outputs nicely\"\n", "\n", "    ```python\n", "    from langchain_core.messages import convert_to_messages\n", "    \n", "    \n", "    def pretty_print_message(message, indent=False):\n", "        pretty_message = message.pretty_repr(html=True)\n", "        if not indent:\n", "            print(pretty_message)\n", "            return\n", "    \n", "        indented = \"\\n\".join(\"\\t\" + c for c in pretty_message.split(\"\\n\"))\n", "        print(indented)\n", "    \n", "    \n", "    def pretty_print_messages(update, last_message=False):\n", "        is_subgraph = False\n", "        if isinstance(update, tuple):\n", "            ns, update = update\n", "            # skip parent graph updates in the printouts\n", "            if len(ns) == 0:\n", "                return\n", "    \n", "            graph_id = ns[-1].split(\":\")[0]\n", "            print(f\"Update from subgraph {graph_id}:\")\n", "            print(\"\\n\")\n", "            is_subgraph = True\n", "    \n", "        for node_name, node_update in update.items():\n", "            update_label = f\"Update from node {node_name}:\"\n", "            if is_subgraph:\n", "                update_label = \"\\t\" + update_label\n", "    \n", "            print(update_label)\n", "            print(\"\\n\")\n", "    \n", "            messages = convert_to_messages(node_update[\"messages\"])\n", "            if last_message:\n", "                messages = messages[-1:]\n", "    \n", "            for m in messages:\n", "                pretty_print_message(m, indent=is_subgraph)\n", "            print(\"\\n\")\n", "    ```"]}, {"cell_type": "code", "execution_count": 5, "id": "bfaa81e6", "metadata": {}, "outputs": [], "source": ["# hide-cell\n", "from langchain_core.messages import convert_to_messages\n", "\n", "\n", "def pretty_print_message(message, indent=False):\n", "    pretty_message = message.pretty_repr(html=True)\n", "    if not indent:\n", "        print(pretty_message)\n", "        return\n", "\n", "    indented = \"\\n\".join(\"\\t\" + c for c in pretty_message.split(\"\\n\"))\n", "    print(indented)\n", "\n", "\n", "def pretty_print_messages(update, last_message=False):\n", "    is_subgraph = False\n", "    if isinstance(update, tuple):\n", "        ns, update = update\n", "        # skip parent graph updates in the printouts\n", "        if len(ns) == 0:\n", "            return\n", "\n", "        graph_id = ns[-1].split(\":\")[0]\n", "        print(f\"Update from subgraph {graph_id}:\")\n", "        print(\"\\n\")\n", "        is_subgraph = True\n", "\n", "    for node_name, node_update in update.items():\n", "        update_label = f\"Update from node {node_name}:\"\n", "        if is_subgraph:\n", "            update_label = \"\\t\" + update_label\n", "\n", "        print(update_label)\n", "        print(\"\\n\")\n", "\n", "        messages = convert_to_messages(node_update[\"messages\"])\n", "        if last_message:\n", "            messages = messages[-1:]\n", "\n", "        for m in messages:\n", "            pretty_print_message(m, indent=is_subgraph)\n", "        print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 6, "id": "71474c5b-6a9b-460e-bf30-77831976aaf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_T4wrj7DKAG5hVtNVhjRYGdei)\n", " Call ID: call_T4wrj7DKAG5hVtNVhjRYGdei\n", "  Args:\n", "    query: current mayor of New York City\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"current mayor of New York City\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"title\": \"List of mayors of New York City - Wikipedia\", \"url\": \"https://en.wikipedia.org/wiki/List_of_mayors_of_New_York_City\", \"content\": \"The mayor of New York City is the chief executive of the Government of New York City, as stipulated by New York City's charter.The current officeholder, the 110th in the sequence of regular mayors, is <PERSON>, a member of the Democratic Party.. During the Dutch colonial period from 1624 to 1664, New Amsterdam was governed by the Director of New Netherland.\", \"score\": 0.9039154, \"raw_content\": null}, {\"title\": \"Office of the Mayor | Mayor's Bio | City of New York - NYC.gov\", \"url\": \"https://www.nyc.gov/office-of-the-mayor/bio.page\", \"content\": \"Mayor <PERSON> has served the people of New York City as an NYPD officer, State Senator, Brooklyn Borough President, and now as the 110th Mayor of the City of New York. He gave voice to a diverse coalition of working families in all five boroughs and is leading the fight to bring back New York City's economy, reduce inequality, improve\", \"score\": 0.8405867, \"raw_content\": null}, {\"title\": \"<PERSON>\", \"url\": \"https://en.wikipedia.org/wiki/<PERSON>_<PERSON>\", \"content\": \"<PERSON> <PERSON> (born September 1, 1960) is an American politician and former police officer who has served as the 110th mayor of New York City since 2022. Adams was an officer in the New York City Transit Police and then the New York City Police Department (NYPD) for more than 20 years, retiring at the rank of captain.He served in the New York State Senate from 2006 to 2013, representing the\", \"score\": 0.77731717, \"raw_content\": null}], \"response_time\": 1.31}\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "The current mayor of New York City is <PERSON>.\n", "\n", "\n"]}], "source": ["for chunk in research_agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"who is the mayor of NYC?\"}]}\n", "):\n", "    pretty_print_messages(chunk)"]}, {"attachments": {}, "cell_type": "markdown", "id": "95e76c28-e9c9-42b7-8381-5e2a07f900cb", "metadata": {}, "source": ["### Math agent\n", "\n", "For math agent tools we will use [vanilla Python functions](../../../agents/tools#define-simple-tools):"]}, {"cell_type": "code", "execution_count": 7, "id": "95910bb8-7144-4015-9ea4-d97d0c22db12", "metadata": {}, "outputs": [], "source": ["def add(a: float, b: float):\n", "    \"\"\"Add two numbers.\"\"\"\n", "    return a + b\n", "\n", "\n", "def multiply(a: float, b: float):\n", "    \"\"\"Multiply two numbers.\"\"\"\n", "    return a * b\n", "\n", "\n", "def divide(a: float, b: float):\n", "    \"\"\"Divide two numbers.\"\"\"\n", "    return a / b\n", "\n", "\n", "math_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[add, multiply, divide],\n", "    prompt=(\n", "        \"You are a math agent.\\n\\n\"\n", "        \"INSTRUCTIONS:\\n\"\n", "        \"- Assist ONLY with math-related tasks\\n\"\n", "        \"- After you're done with your tasks, respond to the supervisor directly\\n\"\n", "        \"- Respond ONLY with the results of your work, do NOT include ANY other text.\"\n", "    ),\n", "    name=\"math_agent\",\n", ")"]}, {"cell_type": "markdown", "id": "0df97bc0-f38b-4893-a135-c16cecbfa759", "metadata": {}, "source": ["Let's run the math agent:"]}, {"cell_type": "code", "execution_count": 8, "id": "f803943c-6dd2-4b42-93ba-0210ec276132", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  add (call_LqyOrR1Ktr2LVLDlXpbWNAsp)\n", " Call ID: call_LqyOrR1Ktr2LVLDlXpbWNAsp\n", "  Args:\n", "    a: 3\n", "    b: 5\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: add\n", "\n", "8.0\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  multiply (call_IBXYtlEMdZrfDZ8g8bWC31pM)\n", " Call ID: call_IBXYtlEMdZrfDZ8g8bWC31pM\n", "  Args:\n", "    a: 8\n", "    b: 7\n", "\n", "\n", "Update from node tools:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "56.0\n", "\n", "\n", "Update from node agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "56\n", "\n", "\n"]}], "source": ["for chunk in math_agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": \"what's (3 + 5) x 7\"}]}\n", "):\n", "    pretty_print_messages(chunk)"]}, {"cell_type": "markdown", "id": "dceefc4c-95e1-4565-8946-4c113a11416a", "metadata": {}, "source": ["## 2. Create supervisor with `langgraph-supervisor`"]}, {"cell_type": "markdown", "id": "eb5637a4-8ecb-4c2c-82f5-3674205e37b5", "metadata": {}, "source": ["To implement out multi-agent system, we will use [`create_supervisor`][langgraph_supervisor.supervisor.create_supervisor] from the prebuilt `langgraph-supervisor` library:"]}, {"cell_type": "code", "execution_count": 9, "id": "6f32a820-1f3d-4698-831b-46252e2316bc", "metadata": {}, "outputs": [], "source": ["from langgraph_supervisor import create_supervisor\n", "from langchain.chat_models import init_chat_model\n", "\n", "supervisor = create_supervisor(\n", "    model=init_chat_model(\"openai:gpt-4.1\"),\n", "    agents=[research_agent, math_agent],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this agent\\n\"\n", "        \"- a math agent. Assign math-related tasks to this agent\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    add_handoff_back_messages=True,\n", "    output_mode=\"full_history\",\n", ").compile()"]}, {"cell_type": "code", "execution_count": 10, "id": "4c45a3c2-4d9e-4760-87bc-3cc2c007a167", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Image\n", "\n", "display(Image(supervisor.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "3a94c3d1-3617-4d68-ac9a-b9bb2c05a604", "metadata": {}, "source": ["Let's now run it with a query that requires both agents:\n", "\n", "* research agent will look up the necessary GDP information\n", "* math agent will perform division to find the percentage of NY state GDP, as requested"]}, {"cell_type": "code", "execution_count": 11, "id": "89b77171-58d0-4b36-8cb3-b69d69e4bdf9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "\n", "\n", "Update from node research_agent:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_back_to_supervisor\n", "\n", "Successfully transferred back to supervisor\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "\n", "\n", "Update from node math_agent:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_back_to_supervisor\n", "\n", "Successfully transferred back to supervisor\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "The US GDP in 2024 was $29.017 trillion, and New York State's GDP was $2.284 trillion. New York State accounted for approximately 7.87% of the total US GDP in 2024.\n", "\n", "\n"]}], "source": ["for chunk in supervisor.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "):\n", "    pretty_print_messages(chunk, last_message=True)\n", "\n", "final_message_history = chunk[\"supervisor\"][\"messages\"]"]}, {"cell_type": "markdown", "id": "599b8d13-70a9-40f3-99b9-5e3d35cc6f56", "metadata": {}, "source": ["## 3. Create supervisor from scratch"]}, {"cell_type": "markdown", "id": "e23764f8-f818-45f3-a5ee-4cb7b7002336", "metadata": {}, "source": ["Let's now implement this same multi-agent system from scratch. We will need to:\n", "\n", "1. Set up how the supervisor [communicates](#set-up-agent-communication) with individual agents\n", "2. Create the [supervisor agent](#create-supervisor-agent)\n", "3. [Co<PERSON><PERSON>]() supervisor and worker agents into a single multi-agent graph."]}, {"cell_type": "markdown", "id": "78d32089-0259-4aa3-a473-077ecce342dd", "metadata": {}, "source": ["### Set up agent communication"]}, {"cell_type": "markdown", "id": "a6c6b436-e95b-476b-b427-7b02d8384cc5", "metadata": {}, "source": ["We will need to define a way for the supervisor agent to communicate with the worker agents. A common way to implement this in multi-agent architectures is using **handoffs**, where one agent *hands off* control to another. Handoffs allow you to specify:\n", "\n", "- **destination**: target agent to transfer to\n", "- **payload**: information to pass to that agent\n", "\n", "We will implement handoffs via **handoff tools** and give these tools to the supervisor agent: when the supervisor calls these tools, it will hand off control to a worker agent, passing the full message history to that agent."]}, {"cell_type": "code", "execution_count": 12, "id": "7873a6c3-7de0-46d1-96ce-2dca7c62245a", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from langchain_core.tools import tool, InjectedToolCallId\n", "from langgraph.prebuilt import InjectedState\n", "from langgraph.graph import StateGraph, START, MessagesState\n", "from langgraph.types import Command\n", "\n", "\n", "def create_handoff_tool(*, agent_name: str, description: str | None = None):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Ask {agent_name} for help.\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        state: Annotated[MessagesState, InjectedState],\n", "        tool_call_id: Annotated[str, InjectedToolCallId],\n", "    ) -> Command:\n", "        tool_message = {\n", "            \"role\": \"tool\",\n", "            \"content\": f\"Successfully transferred to {agent_name}\",\n", "            \"name\": name,\n", "            \"tool_call_id\": tool_call_id,\n", "        }\n", "        # highlight-next-line\n", "        return Command(\n", "            # highlight-next-line\n", "            goto=agent_name,  # (1)!\n", "            # highlight-next-line\n", "            update={**state, \"messages\": state[\"messages\"] + [tool_message]},  # (2)!\n", "            # highlight-next-line\n", "            graph=Command.PARENT,  # (3)!\n", "        )\n", "\n", "    return handoff_tool\n", "\n", "\n", "# Handoffs\n", "assign_to_research_agent = create_handoff_tool(\n", "    agent_name=\"research_agent\",\n", "    description=\"Assign task to a researcher agent.\",\n", ")\n", "\n", "assign_to_math_agent = create_handoff_tool(\n", "    agent_name=\"math_agent\",\n", "    description=\"Assign task to a math agent.\",\n", ")"]}, {"cell_type": "markdown", "id": "3bc810a4-1866-4915-abd9-12655954e673", "metadata": {}, "source": ["1. Name of the agent or node to hand off to.\n", "2. Take the agent's messages and add them to the parent's state as part of the handoff. The next agent will see the parent state.\n", "3. Indicate to <PERSON>G<PERSON><PERSON> that we need to navigate to agent node in a **parent** multi-agent graph."]}, {"cell_type": "markdown", "id": "6a7bd301-c954-4189-87e2-b3bf42d187c1", "metadata": {}, "source": ["### Create supervisor agent"]}, {"cell_type": "markdown", "id": "ca787ce6-46f9-49c3-8f9f-ddb7ff52cdeb", "metadata": {}, "source": ["Then, let's create the supervisor agent with the handoff tools we just defined. We will use the prebuilt [`create_react_agent`][langgraph.prebuilt.chat_agent_executor.create_react_agent]:"]}, {"cell_type": "code", "execution_count": 13, "id": "de5d1ff8-e7a0-4efe-93fe-d304eaef29b6", "metadata": {}, "outputs": [], "source": ["supervisor_agent = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[assign_to_research_agent, assign_to_math_agent],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this agent\\n\"\n", "        \"- a math agent. Assign math-related tasks to this agent\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    name=\"supervisor\",\n", ")"]}, {"cell_type": "markdown", "id": "dd4bc4e2-e133-42fd-a47f-6fb63fa51697", "metadata": {}, "source": ["### Create multi-agent graph"]}, {"cell_type": "markdown", "id": "8e6dcae1-eb91-4a07-aef8-53cbed14a002", "metadata": {}, "source": ["Putting this all together, let's create a graph for our overall multi-agent system. We will add the supervisor and the individual agents as [subgraph](../../../concepts/low_level#subgraphs) nodes."]}, {"cell_type": "code", "execution_count": 14, "id": "584443af-587a-44f3-a320-f942868191e4", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END\n", "\n", "# Define the multi-agent supervisor graph\n", "supervisor = (\n", "    StateGraph(MessagesState)\n", "    # NOTE: `destinations` is only needed for visualization and doesn't affect runtime behavior\n", "    .add_node(supervisor_agent, destinations=(\"research_agent\", \"math_agent\", END))\n", "    .add_node(research_agent)\n", "    .add_node(math_agent)\n", "    .add_edge(START, \"supervisor\")\n", "    # always return back to the supervisor\n", "    .add_edge(\"research_agent\", \"supervisor\")\n", "    .add_edge(\"math_agent\", \"supervisor\")\n", "    .compile()\n", ")"]}, {"cell_type": "markdown", "id": "12576211-a946-46ec-bf81-24215597a1fe", "metadata": {}, "source": ["Notice that we've added explicit [edges](../../../concepts/low_level#edges) from worker agents back to the supervisor — this means that they are guaranteed to return control back to the supervisor. If you want the agents to respond directly to the user (i.e., turn the system into a router, you can remove these edges)."]}, {"cell_type": "code", "execution_count": 15, "id": "0175fe14-5854-4197-b7e8-559335d0f81b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Image\n", "\n", "display(Image(supervisor.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "d36496de-7121-4c49-8cb6-58c943c66628", "metadata": {}, "source": ["With the multi-agent graph created, let's now run it!"]}, {"cell_type": "code", "execution_count": 16, "id": "56ba78e9-d9c1-457c-a073-d606d5d3e013", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "\n", "\n", "Update from node research_agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "- US GDP in 2024: $29.017 trillion (nominal, current prices)\n", "- New York state GDP in 2024: $2.284 trillion\n", "- New York state's share of US GDP in 2024: 7.87%\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "\n", "\n", "Update from node math_agent:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "US GDP in 2024: $29.017 trillion\n", "New York state GDP in 2024: $2.284 trillion\n", "New York state's share of US GDP: 7.87%\n", "\n", "\n", "Update from node supervisor:\n", "\n", "\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "US GDP in 2024 was $29.017 trillion. New York state GDP in 2024 was $2.284 trillion. New York state's GDP was about 7.87% of the US GDP.\n", "\n", "\n"]}], "source": ["for chunk in supervisor.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "):\n", "    pretty_print_messages(chunk, last_message=True)\n", "\n", "final_message_history = chunk[\"supervisor\"][\"messages\"]"]}, {"cell_type": "markdown", "id": "dac583a8-af2c-402f-801e-15a6bc318e44", "metadata": {}, "source": ["Let's examine the full resulting message history:"]}, {"cell_type": "code", "execution_count": 17, "id": "1eff504c-7437-477e-ab28-936c1238<PERSON>ca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "find US and New York state GDP in 2024. what % of US GDP was New York state?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "Tool Calls:\n", "  transfer_to_research_agent (call_qxk9abrxWYQT6a9hPNpXiuM0)\n", " Call ID: call_qxk9abrxWYQT6a9hPNpXiuM0\n", "  Args:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_research_agent\n", "\n", "Successfully transferred to research_agent\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "Tool Calls:\n", "  tavily_search (call_A3cVm1BXDD8dYv6uLwO132gg)\n", " Call ID: call_A3cVm1BXDD8dYv6uLwO132gg\n", "  Args:\n", "    query: US GDP 2024\n", "    search_depth: advanced\n", "  tavily_search (call_77JyoUYwGDXlRNKOwvQFUUYJ)\n", " Call ID: call_77JyoUYwGDXlRNKOwvQFUUYJ\n", "  Args:\n", "    query: New York state GDP 2024\n", "    search_depth: advanced\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"US GDP 2024\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://tradingeconomics.com/united-states/gdp-growth\", \"title\": \"United States GDP Growth Rate - Trading Economics\", \"content\": \"The US economy expanded an annualized 2.4% in Q4 2024, slightly higher than 2.3% in the previous estimates, primarily reflecting a downward revision to imports. Exports fell slightly less (-0.2% vs -0.5%) and imports declined more than initially anticipated (-1.9% vs -1.2%), leaving the contribution from net trade positive at 0.26 pp (vs 0.12 pp). Government expenditure also rose more (3.1% vs 2.9%) and fixed investment contracted less (-1.1% vs -1.4%), due to equipment (-8.7% vs -9%) while [...] The Gross Domestic Product (GDP) in the United States expanded 2.40 percent in the fourth quarter of 2024 over the previous quarter. GDP Growth Rate in the United States averaged 3.21 percent from 1947 until 2024, reaching an all time high of 35.20 percent in the third quarter of 2020 and a record low of -28.10 percent in the second quarter of 2020. This page provides the latest reported value for - United States GDP Growth Rate - plus previous releases, historical high and low, short-term [...] The Gross Domestic Product (GDP) in the United States expanded 2.40 percent in the fourth quarter of 2024 over the previous quarter. GDP Growth Rate in the United States is expected to be 0.90 percent by the end of this quarter, according to Trading Economics global macro models and analysts expectations. In the long-term, the United States GDP Growth Rate is projected to trend around 2.00 percent in 2026, according to our econometric models.\\n%\\n3Y5Y10YMAX\\nExport API\\nOK\\nLoading...\", \"score\": 0.9071234, \"raw_content\": null}, {\"url\": \"https://www.bea.gov/data/gdp/gross-domestic-product\", \"title\": \"Gross Domestic Product | U.S. Bureau of Economic Analysis (BEA)\", \"content\": \"Real gross domestic product (GDP) increased at an annual rate of 2.4 percent in the fourth quarter of 2024 (October, November, and December), according to the third estimate released by the U.S. Bureau of Economic Analysis. In the third quarter, real GDP increased 3.1 percent. The increase in real GDP in the fourth quarter primarily reflected increases in consumer spending and government spending that were partly offset by a decrease in investment. Imports, which are a subtraction in the\", \"score\": 0.9008183, \"raw_content\": null}, {\"url\": \"https://www.nerdwallet.com/article/finance/gdp-report\", \"title\": \"GDP Report: Final Estimate Shows Growth Increased 2.4% in Q4 2024\", \"content\": \"NerdWallet's content is fact-checked for accuracy, timeliness and relevance. It undergoes a thorough review process involving writers and editors to ensure the information is as clear and complete as possible.\\n\\nAnna Helhoski\\n\\nRick VanderKnyff\\n\\nUpdated on April 7\\n\\nReal gross domestic product increased by an annual rate of 2.4% in the fourth quarter of 2024, according to the third estimate report released on March 27 by the Bureau of Economic Analysis. [...] The third estimate also showed that in 2024, the U.S. GDP grew 2.8% compared to a 2.9% increase in 2023.\\n\\nQ1 2025 GDP forecasts are negative\\n\\nA forecasting tool published by the Atlanta Federal Reserve shows negative growth for the first quarter of 2025; it expects gross domestic product (GDP) to contract by rate of -2.8%, according to data released on March 28. If the forecast is correct, it would be the first quarter where GDP declined since the first and second quarters of 2022. [...] GDP could be the next concern: On March 3, a forecasting tool published by the Atlanta Federal Reserve showed that GDP could contract by a rate of -2.8% in the first quarter of 2025. If GDP does indeed decline, it would be the first time since the first and second quarters of 2022.\\n\\nIn 2024, the U.S. GDP grew 2.8% compared to a 2.9% increase in 2023, according to a second estimate of real gross domestic product from the Bureau of Economic Analysis, released Feb. 27.\", \"score\": 0.********, \"raw_content\": null}], \"response_time\": 1.78}\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search\n", "\n", "{\"query\": \"New York state GDP 2024\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://en.wikipedia.org/wiki/Economy_of_New_York_(state)\", \"title\": \"Economy of New York (state) - Wikipedia\", \"content\": \"Jump to content\\nMain menu\\nSearch\\nDonate\\nCreate account\\nLog in\\nPersonal tools\\nToggle the table of contents\\nEconomy of New York (state)\\n1 language\\nArticle\\nTalk\\nRead\\nEdit\\nView history\\nTools\\nFrom Wikipedia, the free encyclopedia\\nThis article is about the overall economy of New York State. For the economy of New York City, see Economy of New York City.\\nEconomy of New York\\nNew York City, the economic capital of New York (state)\\nStatistics\\nGDP $2.3 trillion (2024)[1]\\nGDP per capita  $117,332 (2024)[2] [...] The economy of the State of New York is reflected in its gross state product in 2024 of $2.284 trillion, ranking third in size behind the larger states of California and Texas. If New York State were an independent nation, it would rank as the 10th largest economy in the world by nominal GDP. However, in 2019, the multi-state, New York City-centered metropolitan statistical area produced a gross metropolitan product (GMP) of $US2.0 trillion, ranking first nationally by a wide margin and would [...] Population below poverty line   13.6%[3]\\nGini coefficient    0.5157 ± 0.0029 (2023)[4]\\nLabour force    9,645,984 (2023)[5]\\nUnemployment    4.4% (August 2024)[6]\\nPublic finances\\nRevenues    $63.5 billion[7]\\nExpenses    $54.6 billion[8]\", \"score\": 0.9511106, \"raw_content\": null}, {\"url\": \"https://en.wikipedia.org/wiki/List_of_U.S._states_and_territories_by_GDP\", \"title\": \"List of U.S. states and territories by GDP - Wikipedia\", \"content\": \"GDP per capita also varied widely throughout the United States in 2024, with New York ($117,332), Massachusetts ($110,561), and Washington (state) ($108,468) recording the three highest GDP per capita figures in the U.S., while Mississippi ($53,061), Arkansas ($60,276), and West Virginia ($60,783) recorded the three lowest GDP per capita figures in the U.S. The District of Columbia, though, recorded a GDP per capita figure far higher than any U.S. state in 2024 at $263,220. [...] Overall, in the calendar year 2024, the United States' Nominal GDP at Current Prices totaled at $29.017 trillion, as compared to $25.744 trillion in 2022.\\nThe three U.S. states with the highest GDPs were California ($4.080 trillion), Texas ($2.695 trillion), and New York ($2.284 trillion). The three U.S. states with the lowest GDPs were Vermont ($45.4 billion), Wyoming ($53.0 billion), and Alaska ($69.8 billion).\", \"score\": 0.8947989, \"raw_content\": null}, {\"url\": \"https://edc.nyc/sites/default/files/2025-01/NYCEDC-State-of-the-NYC-Economy-2024-v3.pdf\", \"title\": \"[PDF] State of the New York City Economy - NYCEDC\", \"content\": \"for talent faced a limited supply. STATE OF THE NEW YORK CITY ECONOMY 2024 / 21 STATE OF THE NEW YORK CITY ECONOMY 2024 / 22 After losing nearly a million jobs during the COVID-19 pandemic, New York City is now at record-high levels of private and total employment. The city’s gross city product (GCP) stands at $1.18 trillion as of 2023.24 While legacy sectors such as Finance and Insurance have continued to play a vital part in the city’s economic successes, emerging sectors like Tech, the Green [...] STATE OF THE NEW YORK CITY ECONOMY 2024 / 11 New York City’s economy is the largest in the nation, with $2 trillion in gross metropolitan product (GMP) for the metro area, representing 9% of the total US economy. As such, the city’s economy is closely intertwined with the broader national economic landscape, and US macroeconomic conditions play a significant role in shaping the economic backdrop for the city. National interest rates, inflation, gross domestic product (GDP), and employment [...] 1 Macro Trends STATE OF THE NEW YORK CITY ECONOMY 2024 / 12 Output Has Grown Faster in NYC than Nationally Since 2022 Actual and Projected US Real GDP and NYC Real GCP Growth Rates 2019 2020 2021 2022 2023 2024* 2025* NYC 2.6% -4.2% 5.5% 2.6% 2.8% 3.6% 1.9% US 2.5% -2.2% 5.8% 1.9% 2.5% 2.5% 1.4% Source: NYC OMB and US Bureau of Economic Analysis. Projections for 2024 and and 2025 from NYC OMB.\", \"score\": 0.85797083, \"raw_content\": null}], \"response_time\": 0.63}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: research_agent\n", "\n", "- US GDP in 2024: $29.017 trillion (nominal, current prices)\n", "- New York state GDP in 2024: $2.284 trillion\n", "- New York state's share of US GDP in 2024: 7.87%\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "Tool Calls:\n", "  transfer_to_math_agent (call_m5ICqaoAtRXHWb8BI3638dJL)\n", " Call ID: call_m5ICqaoAtRXHWb8BI3638dJL\n", "  Args:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: transfer_to_math_agent\n", "\n", "Successfully transferred to math_agent\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "Tool Calls:\n", "  divide (call_Sf7nvyvEgIaoDlaioPmkCdqz)\n", " Call ID: call_Sf7nvyvEgIaoDlaioPmkCdqz\n", "  Args:\n", "    a: 2.284\n", "    b: 29.017\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: divide\n", "\n", "0.07871247889168417\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: math_agent\n", "\n", "US GDP in 2024: $29.017 trillion\n", "New York state GDP in 2024: $2.284 trillion\n", "New York state's share of US GDP: 7.87%\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: supervisor\n", "\n", "US GDP in 2024 was $29.017 trillion. New York state GDP in 2024 was $2.284 trillion. New York state's GDP was about 7.87% of the US GDP.\n"]}], "source": ["for message in final_message_history:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "239700cd-d2d3-44be-8cf5-021d15615de2", "metadata": {}, "source": ["!!! Important\n", "\n", "    You can see that the supervisor system appends **all** of the individual agent messages (i.e., their internal tool-calling loop) to the full message history. This means that on every supervisor turn, supervisor agent sees this full history. If you want more control over:\n", "\n", "    * **how inputs are passed to agents**: you can use LangGraph [`Send()`][langgraph.types.Send] primitive to directly send data to the worker agents during the handoff. See the [task delegation](#create-delegation-tasks) example below\n", "    * **how agent outputs are added**: you can control how much of the agent's internal message history is added to the overall supervisor message history by wrapping the agent in a separate node function:\n", "\n", "        ```python\n", "        def call_research_agent(state):\n", "            # return agent's final response,\n", "            # excluding inner monologue\n", "            response = research_agent.invoke(state)\n", "            # highlight-next-line\n", "            return {\"messages\": response[\"messages\"][-1]}\n", "        ```"]}, {"cell_type": "markdown", "id": "1c2a0aff-935a-46b8-bdcf-7125b80f1f41", "metadata": {}, "source": ["## 4. Create delegation tasks"]}, {"cell_type": "markdown", "id": "1a13d919-49bf-4cc3-838f-714cd3fb8b26", "metadata": {}, "source": ["So far the individual agents relied on **interpreting full message history** to determine their tasks. An alternative approach is to ask the supervisor to **formulate a task explicitly**. We can do so by adding a `task_description` parameter to the `handoff_tool` function."]}, {"cell_type": "code", "execution_count": 18, "id": "dc1e4d24-8738-4548-a5e7-8556e476b805", "metadata": {}, "outputs": [], "source": ["from langgraph.types import Send\n", "\n", "\n", "def create_task_description_handoff_tool(\n", "    *, agent_name: str, description: str | None = None\n", "):\n", "    name = f\"transfer_to_{agent_name}\"\n", "    description = description or f\"Ask {agent_name} for help.\"\n", "\n", "    @tool(name, description=description)\n", "    def handoff_tool(\n", "        # this is populated by the supervisor <PERSON><PERSON>\n", "        task_description: Annotated[\n", "            str,\n", "            \"Description of what the next agent should do, including all of the relevant context.\",\n", "        ],\n", "        # these parameters are ignored by the LLM\n", "        state: Annotated[MessagesState, InjectedState],\n", "    ) -> Command:\n", "        task_description_message = {\"role\": \"user\", \"content\": task_description}\n", "        agent_input = {**state, \"messages\": [task_description_message]}\n", "        return Command(\n", "            # highlight-next-line\n", "            goto=[Send(agent_name, agent_input)],\n", "            graph=Command.PARENT,\n", "        )\n", "\n", "    return handoff_tool\n", "\n", "\n", "assign_to_research_agent_with_description = create_task_description_handoff_tool(\n", "    agent_name=\"research_agent\",\n", "    description=\"Assign task to a researcher agent.\",\n", ")\n", "\n", "assign_to_math_agent_with_description = create_task_description_handoff_tool(\n", "    agent_name=\"math_agent\",\n", "    description=\"Assign task to a math agent.\",\n", ")\n", "\n", "supervisor_agent_with_description = create_react_agent(\n", "    model=\"openai:gpt-4.1\",\n", "    tools=[\n", "        assign_to_research_agent_with_description,\n", "        assign_to_math_agent_with_description,\n", "    ],\n", "    prompt=(\n", "        \"You are a supervisor managing two agents:\\n\"\n", "        \"- a research agent. Assign research-related tasks to this assistant\\n\"\n", "        \"- a math agent. Assign math-related tasks to this assistant\\n\"\n", "        \"Assign work to one agent at a time, do not call agents in parallel.\\n\"\n", "        \"Do not do any work yourself.\"\n", "    ),\n", "    name=\"supervisor\",\n", ")\n", "\n", "supervisor_with_description = (\n", "    StateGraph(MessagesState)\n", "    .add_node(\n", "        supervisor_agent_with_description, destinations=(\"research_agent\", \"math_agent\")\n", "    )\n", "    .add_node(research_agent)\n", "    .add_node(math_agent)\n", "    .add_edge(START, \"supervisor\")\n", "    .add_edge(\"research_agent\", \"supervisor\")\n", "    .add_edge(\"math_agent\", \"supervisor\")\n", "    .compile()\n", ")"]}, {"cell_type": "markdown", "id": "622bc89a-0787-41ea-bf18-c591be85509b", "metadata": {}, "source": ["!!! note\n", "\n", "    We're using [`Send()`][langgraph.types.Send] primitive in the `handoff_tool`. This means that instead of receiving the full `supervisor` graph state as input, each worker agent only sees the contents of the `Send` payload. In this example, we're sending the task description as a single \"human\" message."]}, {"cell_type": "markdown", "id": "4d1dba40-c948-4117-9df6-5bdb1ab162c7", "metadata": {}, "source": ["Let's now running it with the same input query:"]}, {"cell_type": "code", "execution_count": 19, "id": "882f9e00-5fdf-4278-ab26-a59fc949fa91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\tTool Calls:\n", "\t  transfer_to_research_agent (call_TtKzjGQBe4X9Xh0VzmjStVgZ)\n", "\t Call ID: call_TtKzjGQBe4X9Xh0VzmjStVgZ\n", "\t  Args:\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: research_agent\n", "\tTool Calls:\n", "\t  tavily_search (call_AfeRYBJxJtmD4EKqifYcx8EI)\n", "\t Call ID: call_AfeRYBJxJtmD4EKqifYcx8EI\n", "\t  Args:\n", "\t    query: US GDP in 2024\n", "\t    search_depth: advanced\n", "\t  tavily_search (call_n7Dn8QnDLu2ZpEDzswS2MOJ8)\n", "\t Call ID: call_n7Dn8QnDLu2ZpEDzswS2MOJ8\n", "\t  Args:\n", "\t    query: New York state GDP in 2024\n", "\t    search_depth: advanced\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: tavily_search\n", "\t\n", "\t{\"query\": \"New York state GDP in 2024\", \"follow_up_questions\": null, \"answer\": null, \"images\": [], \"results\": [{\"url\": \"https://en.wikipedia.org/wiki/Economy_of_New_York_(state)\", \"title\": \"Economy of New York (state) - Wikipedia\", \"content\": \"Jump to content\\nMain menu\\nSearch\\nDonate\\nCreate account\\nLog in\\nPersonal tools\\nToggle the table of contents\\nEconomy of New York (state)\\n1 language\\nArticle\\nTalk\\nRead\\nEdit\\nView history\\nTools\\nFrom Wikipedia, the free encyclopedia\\nThis article is about the overall economy of New York State. For the economy of New York City, see Economy of New York City.\\nEconomy of New York\\nNew York City, the economic capital of New York (state)\\nStatistics\\nGDP $2.3 trillion (2024)[1]\\nGDP per capita  $117,332 (2024)[2] [...] The economy of the State of New York is reflected in its gross state product in 2024 of $2.284 trillion, ranking third in size behind the larger states of California and Texas. If New York State were an independent nation, it would rank as the 10th largest economy in the world by nominal GDP. However, in 2019, the multi-state, New York City-centered metropolitan statistical area produced a gross metropolitan product (GMP) of $US2.0 trillion, ranking first nationally by a wide margin and would [...] Population below poverty line   13.6%[3]\\nGini coefficient    0.5157 ± 0.0029 (2023)[4]\\nLabour force    9,645,984 (2023)[5]\\nUnemployment    4.4% (August 2024)[6]\\nPublic finances\\nRevenues    $63.5 billion[7]\\nExpenses    $54.6 billion[8]\", \"score\": 0.9530353, \"raw_content\": null}, {\"url\": \"https://en.wikipedia.org/wiki/List_of_U.S._states_and_territories_by_GDP\", \"title\": \"List of U.S. states and territories by GDP - Wikipedia\", \"content\": \"GDP per capita also varied widely throughout the United States in 2024, with New York ($117,332), Massachusetts ($110,561), and Washington (state) ($108,468) recording the three highest GDP per capita figures in the U.S., while Mississippi ($53,061), Arkansas ($60,276), and West Virginia ($60,783) recorded the three lowest GDP per capita figures in the U.S. The District of Columbia, though, recorded a GDP per capita figure far higher than any U.S. state in 2024 at $263,220. [...] Overall, in the calendar year 2024, the United States' Nominal GDP at Current Prices totaled at $29.017 trillion, as compared to $25.744 trillion in 2022.\\nThe three U.S. states with the highest GDPs were California ($4.080 trillion), Texas ($2.695 trillion), and New York ($2.284 trillion). The three U.S. states with the lowest GDPs were Vermont ($45.4 billion), Wyoming ($53.0 billion), and Alaska ($69.8 billion).\", \"score\": 0.89997756, \"raw_content\": null}, {\"url\": \"https://edc.nyc/sites/default/files/2025-01/NYCEDC-State-of-the-NYC-Economy-2024-v3.pdf\", \"title\": \"[PDF] State of the New York City Economy - NYCEDC\", \"content\": \"for talent faced a limited supply. STATE OF THE NEW YORK CITY ECONOMY 2024 / 21 STATE OF THE NEW YORK CITY ECONOMY 2024 / 22 After losing nearly a million jobs during the COVID-19 pandemic, New York City is now at record-high levels of private and total employment. The city’s gross city product (GCP) stands at $1.18 trillion as of 2023.24 While legacy sectors such as Finance and Insurance have continued to play a vital part in the city’s economic successes, emerging sectors like Tech, the Green [...] STATE OF THE NEW YORK CITY ECONOMY 2024 / 11 New York City’s economy is the largest in the nation, with $2 trillion in gross metropolitan product (GMP) for the metro area, representing 9% of the total US economy. As such, the city’s economy is closely intertwined with the broader national economic landscape, and US macroeconomic conditions play a significant role in shaping the economic backdrop for the city. National interest rates, inflation, gross domestic product (GDP), and employment [...] 1 Macro Trends STATE OF THE NEW YORK CITY ECONOMY 2024 / 12 Output Has Grown Faster in NYC than Nationally Since 2022 Actual and Projected US Real GDP and NYC Real GCP Growth Rates 2019 2020 2021 2022 2023 2024* 2025* NYC 2.6% -4.2% 5.5% 2.6% 2.8% 3.6% 1.9% US 2.5% -2.2% 5.8% 1.9% 2.5% 2.5% 1.4% Source: NYC OMB and US Bureau of Economic Analysis. Projections for 2024 and and 2025 from NYC OMB.\", \"score\": 0.8616433, \"raw_content\": null}], \"response_time\": 2.35}\n", "\n", "\n", "Update from subgraph research_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: research_agent\n", "\t\n", "\t- US GDP in 2024 was $29.017 trillion (nominal, current prices) ([source](https://en.wikipedia.org/wiki/List_of_U.S._states_and_territories_by_GDP)).\n", "\t- New York State GDP in 2024 was $2.284 trillion ([source](https://en.wikipedia.org/wiki/Economy_of_New_York_(state))).\n", "\t\n", "\tPercentage of US GDP attributed to New York State in 2024: approximately 7.9%.\n", "\n", "\n", "Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\tTool Calls:\n", "\t  transfer_to_math_agent (call_oYbIXhQQeTWlj2zvZSoStUgO)\n", "\t Call ID: call_oYbIXhQQeTWlj2zvZSoStUgO\n", "\t  Args:\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: math_agent\n", "\tTool Calls:\n", "\t  divide (call_K5QxmkQYFfCZw5Vzkbz43VIG)\n", "\t Call ID: call_K5QxmkQYFfCZw5Vzkbz43VIG\n", "\t  Args:\n", "\t    a: 2.284\n", "\t    b: 29.017\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node tools:\n", "\n", "\n", "\t=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\tName: divide\n", "\t\n", "\t0.07871247889168417\n", "\n", "\n", "Update from subgraph math_agent:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: math_agent\n", "\t\n", "\tNew York state's GDP was approximately 7.87% of US GDP in 2024.\n", "\n", "\n", "Update from subgraph supervisor:\n", "\n", "\n", "\tUpdate from node agent:\n", "\n", "\n", "\t==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\tName: supervisor\n", "\t\n", "\tHere are the findings:\n", "\t- US GDP in 2024: $29.017 trillion\n", "\t- New York State GDP in 2024: $2.284 trillion\n", "\t- New York State accounted for approximately 7.87% of US GDP in 2024.\n", "\n", "\n"]}], "source": ["for chunk in supervisor.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"find US and New York state GDP in 2024. what % of US GDP was New York state?\",\n", "            }\n", "        ]\n", "    },\n", "    subgraphs=True,\n", "):\n", "    pretty_print_messages(chunk, last_message=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}