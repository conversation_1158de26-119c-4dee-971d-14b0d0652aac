{"cells": [{"attachments": {"7ad1a116-28d7-473f-8cff-5f2efd0bf118.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "46397e48-aba2-4bd3-bb31-d840f2fcbda4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["# Agentic RAG\n", "\n", "In this tutorial we will build a [retrieval agent](https://python.langchain.com/docs/tutorials/qa_chat_history). Retrieval agents are useful when you want an LLM to make a decision about whether to retrieve context from a vectorstore or respond to the user directly.\n", "\n", "By the end of the tutorial we will have done the following:\n", "\n", "1. Fetch and preprocess documents that will be used for retrieval.\n", "2. Index those documents for semantic search and create a retriever tool for the agent.\n", "3. Build an agentic RAG system that can decide when to use the retriever tool.\n", "\n", "![Screenshot 2024-02-14 at 3.43.58 PM.png](attachment:7ad1a116-28d7-473f-8cff-5f2efd0bf118.png)\n", "\n", "## Setup\n", "\n", "Let's download the required packages and set our API keys:"]}, {"cell_type": "code", "execution_count": 1, "id": "969fb438", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U --quiet langgraph \"langchain[openai]\" langchain-community langchain-text-splitters"]}, {"cell_type": "code", "execution_count": null, "id": "e4958a8c", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(key: str):\n", "    if key not in os.environ:\n", "        os.environ[key] = getpass.getpass(f\"{key}:\")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "3d07e8d4", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "c74e4532", "metadata": {}, "source": ["## 1. Preprocess documents\n", "\n", "1\\. Fetch documents to use in our RAG system. We will use three of the most recent pages from [<PERSON><PERSON>'s excellent blog](https://lilianweng.github.io/). We'll start by fetching the content of the pages using `WebBaseLoader` utility:"]}, {"cell_type": "code", "execution_count": null, "id": "53ff068e-053f-41da-a8b5-b23efc2442fd", "metadata": {"scrolled": true}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader\n", "\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2024-11-28-reward-hacking/\",\n", "    \"https://lilianweng.github.io/posts/2024-07-07-hallucination/\",\n", "    \"https://lilianweng.github.io/posts/2024-04-12-diffusion-video/\",\n", "]\n", "\n", "docs = [WebBaseLoader(url).load() for url in urls]"]}, {"cell_type": "code", "execution_count": 4, "id": "0e027561-c144-469c-913d-c3d8295500ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Reward Hacking in Reinforcement Learning | Lil'Log\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nLil'Log\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n|\\n\\n\\n\\n\\n\\n\\nPosts\\n\\n\\n\\n\\nArchive\\n\\n\\n\\n\\nSearch\\n\\n\\n\\n\\nTags\\n\\n\\n\\n\\nFAQ\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n      Reward Hacking in Reinforcement Learning\\n    \\nDate: November 28, 2024  |  Estimated Reading Time: 37 min  |  Author: <PERSON><PERSON>g\\n\\n\\n \\n\\n\\nTable of Contents\\n\\n\\n\\nBackground\\n\\nReward Function in RL\\n\\nSpurious Correlation\\n\\n\\nLet’s Define Reward Hacking\\n\\nList of Examples\\n\\nReward hacking examples in RL tasks\\n\\nReward hacking examples in LLM tasks\\n\\nReward hacking examples in real life\\n\\n\\nWhy does Reward Hacking Exist?\\n\\n\\nHacking RL Environment\\n\\nHacking RLHF of LLMs\\n\\nHacking the Training Process\\n\\nHacking the Evaluator\\n\\nIn-Context Reward Hacking\\n\\n\\nGeneralization of Hacking Skills\\n\\nPeek into Mitigations\\n\\nRL Algorithm Improvement\\n\\nDetecting Reward Hacking\\n\\nData Analysis of RLHF\\n\\n\\nCitation\\n\\nReferences\\n\\n\\n\\n\\n\\nReward hacking occurs when a reinforcement learning (RL) agent exploits flaws or ambiguities in the reward function to ac\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0][0].page_content.strip()[:1000]"]}, {"cell_type": "markdown", "id": "ca258767-ce4f-4eaa-8ae5-25e9361675e1", "metadata": {}, "source": ["2\\. Split the fetched documents into smaller chunks for indexing into our vectorstore:"]}, {"cell_type": "code", "execution_count": 5, "id": "24d2cefd-d0f0-44ae-aa4b-0436a675dc56", "metadata": {}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=100, chunk_overlap=50\n", ")\n", "doc_splits = text_splitter.split_documents(docs_list)"]}, {"cell_type": "code", "execution_count": 6, "id": "a82479d9-e7d3-481f-893d-0a95c404f0bf", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Reward Hacking in Reinforcement Learning | Lil'Log\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nLil'Log\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n|\\n\\n\\n\\n\\n\\n\\nPosts\\n\\n\\n\\n\\nArchive\\n\\n\\n\\n\\nSearch\\n\\n\\n\\n\\nTags\\n\\n\\n\\n\\nFAQ\""]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["doc_splits[0].page_content.strip()"]}, {"cell_type": "markdown", "id": "c5e7b434-c4c3-4a42-aa4e-2a0bb1e7a2a0", "metadata": {}, "source": ["## 2. Create a retriever tool"]}, {"cell_type": "markdown", "id": "732d6512-51af-426d-995f-98ae72eff4af", "metadata": {}, "source": ["Now that we have our split documents, we can index them into a vector store that we'll use for semantic search. \n", "\n", "1\\. Use an in-memory vector store and OpenAI embeddings:"]}, {"cell_type": "code", "execution_count": 7, "id": "e50c9efe-4abe-42fa-b35a-05eeeede9ec6", "metadata": {}, "outputs": [], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "vectorstore = InMemoryVectorStore.from_documents(\n", "    documents=doc_splits, embedding=OpenAIEmbeddings()\n", ")\n", "retriever = vectorstore.as_retriever()"]}, {"cell_type": "markdown", "id": "225d2277-45b2-4ae8-a7d6-62b07fb4a002", "metadata": {}, "source": ["2\\. Create a retriever tool using <PERSON><PERSON><PERSON><PERSON>'s prebuilt `create_retriever_tool`:"]}, {"cell_type": "code", "execution_count": 8, "id": "0b97bdd8-d7e3-444d-ac96-5ef4725f9048", "metadata": {}, "outputs": [], "source": ["from langchain.tools.retriever import create_retriever_tool\n", "\n", "retriever_tool = create_retriever_tool(\n", "    retriever,\n", "    \"retrieve_blog_posts\",\n", "    \"Search and return information about <PERSON><PERSON> blog posts.\",\n", ")"]}, {"cell_type": "markdown", "id": "9cfdc027-5ea8-42ba-b0f4-0590505d76ff", "metadata": {}, "source": ["3\\. Test the tool:"]}, {"cell_type": "code", "execution_count": 9, "id": "58c17a90-9a85-46c9-96a9-1e7b224330c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'(Note: Some work defines reward tampering as a distinct category of misalignment behavior from reward hacking. But I consider reward hacking as a broader concept here.)\\nAt a high level, reward hacking can be categorized into two types: environment or goal misspecification, and reward tampering.\\n\\nWhy does Reward Hacking Exist?#\\n\\<PERSON><PERSON><PERSON> et al. (2022) investigated reward hacking as a function of agent capabilities, including (1) model size, (2) action space resolution, (3) observation space noise, and (4) training time. They also proposed a taxonomy of three types of misspecified proxy rewards:\\n\\nLet’s Define Reward Hacking#\\nReward shaping in RL is challenging. Reward hacking occurs when an RL agent exploits flaws or ambiguities in the reward function to obtain high rewards without genuinely learning the intended behaviors or completing the task as designed. In recent years, several related concepts have been proposed, all referring to some form of reward hacking:'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["retriever_tool.invoke({\"query\": \"types of reward hacking\"})"]}, {"cell_type": "markdown", "id": "7c4e9e70-3de1-4ab4-ab42-c298cef00eb6", "metadata": {}, "source": ["## 3. Generate query"]}, {"cell_type": "markdown", "id": "2ba89a91-3dc5-4663-a6a5-43ccb2575849", "metadata": {}, "source": ["Now we will start building components ([nodes](../../../concepts/low_level#nodes) and [edges](../../../concepts/low_level#edges)) for our agentic RAG graph. Note that the components will operate on the [`MessagesState`](../../../concepts/low_level#messagesstate) — graph state that contains a `messages` key with a list of [chat messages](https://python.langchain.com/docs/concepts/messages/).\n", "\n", "1\\. Build a `generate_query_or_respond` node. It will call an LLM to generate a response based on the current graph state (list of messages). Given the input messages, it will decide to retrieve using the retriever tool, or respond directly to the user. Note that we're giving the chat model access to the `retriever_tool` we created earlier via `.bind_tools`:"]}, {"cell_type": "code", "execution_count": 10, "id": "32f21d6f-9d0e-44e1-8edd-1fd903164d6d", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "from langchain.chat_models import init_chat_model\n", "\n", "response_model = init_chat_model(\"openai:gpt-4.1\", temperature=0)\n", "\n", "\n", "def generate_query_or_respond(state: MessagesState):\n", "    \"\"\"Call the model to generate a response based on the current state. Given\n", "    the question, it will decide to retrieve using the retriever tool, or simply respond to the user.\n", "    \"\"\"\n", "    response = (\n", "        response_model\n", "        # highlight-next-line\n", "        .bind_tools([retriever_tool]).invoke(state[\"messages\"])\n", "    )\n", "    return {\"messages\": [response]}"]}, {"cell_type": "markdown", "id": "552e0f6c-5af5-4c13-91a6-437f320a4ed6", "metadata": {}, "source": ["2\\. Try it on a random input:"]}, {"cell_type": "code", "execution_count": 11, "id": "5cdc7e76-8680-42e6-9cdb-44aa4104f0f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello! How can I help you today?\n"]}], "source": ["input = {\"messages\": [{\"role\": \"user\", \"content\": \"hello!\"}]}\n", "generate_query_or_respond(input)[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "df45f7c8-df6c-4687-9f25-0357e29532cb", "metadata": {}, "source": ["3\\. Ask a question that requires semantic search:"]}, {"cell_type": "code", "execution_count": 12, "id": "845e1bca-3097-423f-a9c8-967fe1216375", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve_blog_posts (call_tYQxgfIlnQUDMdtAhdbXNwIM)\n", " Call ID: call_tYQxgfIlnQUDMdtAhdbXNwIM\n", "  Args:\n", "    query: types of reward hacking\n"]}], "source": ["input = {\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "        }\n", "    ]\n", "}\n", "generate_query_or_respond(input)[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "3e4756cb-b952-47a7-be4a-4f8a0866b67f", "metadata": {}, "source": ["## 4. Grade documents"]}, {"cell_type": "markdown", "id": "0b278967-18ab-409e-9ca4-6ee44a23e708", "metadata": {}, "source": ["1\\. Add a [conditional edge](../../../concepts/low_level#conditional-edges) — `grade_documents` — to determine whether the retrieved documents are relevant to the question. We will use a model with a structured output schema `GradeDocuments` for document grading. The `grade_documents` function will return the name of the node to go to based on the grading decision (`generate_answer` or `rewrite_question`):"]}, {"cell_type": "code", "execution_count": 13, "id": "98f0e1a5-5ca8-4d8c-a94c-fc0976ae1937", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "from typing import Literal\n", "\n", "GRADE_PROMPT = (\n", "    \"You are a grader assessing relevance of a retrieved document to a user question. \\n \"\n", "    \"Here is the retrieved document: \\n\\n {context} \\n\\n\"\n", "    \"Here is the user question: {question} \\n\"\n", "    \"If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \\n\"\n", "    \"Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.\"\n", ")\n", "\n", "\n", "# highlight-next-line\n", "class GradeDocuments(BaseModel):\n", "    \"\"\"Grade documents using a binary score for relevance check.\"\"\"\n", "\n", "    binary_score: str = Field(\n", "        description=\"Relevance score: 'yes' if relevant, or 'no' if not relevant\"\n", "    )\n", "\n", "\n", "grader_model = init_chat_model(\"openai:gpt-4.1\", temperature=0)\n", "\n", "\n", "def grade_documents(\n", "    state: MessagesState,\n", ") -> Literal[\"generate_answer\", \"rewrite_question\"]:\n", "    \"\"\"Determine whether the retrieved documents are relevant to the question.\"\"\"\n", "    question = state[\"messages\"][0].content\n", "    context = state[\"messages\"][-1].content\n", "\n", "    prompt = GRADE_PROMPT.format(question=question, context=context)\n", "    response = (\n", "        grader_model\n", "        # highlight-next-line\n", "        .with_structured_output(GradeDocuments).invoke(\n", "            [{\"role\": \"user\", \"content\": prompt}]\n", "        )\n", "    )\n", "    score = response.binary_score\n", "\n", "    if score == \"yes\":\n", "        return \"generate_answer\"\n", "    else:\n", "        return \"rewrite_question\""]}, {"cell_type": "markdown", "id": "ddbdc0c2-6f0c-41ec-b4d1-ad20d4498006", "metadata": {}, "source": ["2\\. Run this with irrelevant documents in the tool response:"]}, {"cell_type": "code", "execution_count": 14, "id": "0a62e5f7-4c4e-44cd-b4bd-77b6879ce970", "metadata": {}, "outputs": [{"data": {"text/plain": ["'rewrite_question'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import convert_to_messages\n", "\n", "input = {\n", "    \"messages\": convert_to_messages(\n", "        [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "            },\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": \"\",\n", "                \"tool_calls\": [\n", "                    {\n", "                        \"id\": \"1\",\n", "                        \"name\": \"retrieve_blog_posts\",\n", "                        \"args\": {\"query\": \"types of reward hacking\"},\n", "                    }\n", "                ],\n", "            },\n", "            {\"role\": \"tool\", \"content\": \"meow\", \"tool_call_id\": \"1\"},\n", "        ]\n", "    )\n", "}\n", "grade_documents(input)"]}, {"cell_type": "markdown", "id": "ba25a787-004e-4e6c-8f22-62087af7da1d", "metadata": {}, "source": ["3\\. Confirm that the relevant documents are classified as such:"]}, {"cell_type": "code", "execution_count": 15, "id": "8bd5d24b-f05e-47c4-b954-267ab514a090", "metadata": {}, "outputs": [{"data": {"text/plain": ["'generate_answer'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["input = {\n", "    \"messages\": convert_to_messages(\n", "        [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "            },\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": \"\",\n", "                \"tool_calls\": [\n", "                    {\n", "                        \"id\": \"1\",\n", "                        \"name\": \"retrieve_blog_posts\",\n", "                        \"args\": {\"query\": \"types of reward hacking\"},\n", "                    }\n", "                ],\n", "            },\n", "            {\n", "                \"role\": \"tool\",\n", "                \"content\": \"reward hacking can be categorized into two types: environment or goal misspecification, and reward tampering\",\n", "                \"tool_call_id\": \"1\",\n", "            },\n", "        ]\n", "    )\n", "}\n", "grade_documents(input)"]}, {"cell_type": "markdown", "id": "4c337d45-bc35-4e88-8b27-ebc6631f9fed", "metadata": {}, "source": ["## 5. Rewrite question"]}, {"cell_type": "markdown", "id": "e8d67fdf-0b77-4b90-97f2-63a571c3e496", "metadata": {}, "source": ["1\\. Build the `rewrite_question` node. The retriever tool can return potentially irrelevant documents, which indicates a need to improve the original user question. To do so, we will call the `rewrite_question` node:"]}, {"cell_type": "code", "execution_count": 16, "id": "278d1d83-dda6-4de4-bf8b-be9965c227fa", "metadata": {}, "outputs": [], "source": ["REWRITE_PROMPT = (\n", "    \"Look at the input and try to reason about the underlying semantic intent / meaning.\\n\"\n", "    \"Here is the initial question:\"\n", "    \"\\n ------- \\n\"\n", "    \"{question}\"\n", "    \"\\n ------- \\n\"\n", "    \"Formulate an improved question:\"\n", ")\n", "\n", "\n", "def rewrite_question(state: MessagesState):\n", "    \"\"\"Rewrite the original user question.\"\"\"\n", "    messages = state[\"messages\"]\n", "    question = messages[0].content\n", "    prompt = REWRITE_PROMPT.format(question=question)\n", "    response = response_model.invoke([{\"role\": \"user\", \"content\": prompt}])\n", "    return {\"messages\": [{\"role\": \"user\", \"content\": response.content}]}"]}, {"cell_type": "markdown", "id": "1ec05f29-48a2-4325-8c56-da60490f2a60", "metadata": {}, "source": ["2\\. Try it out:"]}, {"cell_type": "code", "execution_count": 17, "id": "15404075-b7c8-4248-84d3-ca2b361aa3d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What are the different types of reward hacking described by <PERSON><PERSON>, and how does she explain them?\n"]}], "source": ["input = {\n", "    \"messages\": convert_to_messages(\n", "        [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "            },\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": \"\",\n", "                \"tool_calls\": [\n", "                    {\n", "                        \"id\": \"1\",\n", "                        \"name\": \"retrieve_blog_posts\",\n", "                        \"args\": {\"query\": \"types of reward hacking\"},\n", "                    }\n", "                ],\n", "            },\n", "            {\"role\": \"tool\", \"content\": \"meow\", \"tool_call_id\": \"1\"},\n", "        ]\n", "    )\n", "}\n", "\n", "response = rewrite_question(input)\n", "print(response[\"messages\"][-1][\"content\"])"]}, {"cell_type": "markdown", "id": "55eab9fe-f1d6-4ad3-93d6-4638a92c9395", "metadata": {}, "source": ["## 6. Generate an answer"]}, {"cell_type": "markdown", "id": "d9362002-e66e-4e58-90af-d225c184b5d1", "metadata": {}, "source": ["1\\. Build `generate_answer` node: if we pass the grader checks, we can generate the final answer based on the original question and the retrieved context:"]}, {"cell_type": "code", "execution_count": 18, "id": "20b0e60a-a5aa-4e95-a15a-2c91382e508f", "metadata": {}, "outputs": [], "source": ["GENERATE_PROMPT = (\n", "    \"You are an assistant for question-answering tasks. \"\n", "    \"Use the following pieces of retrieved context to answer the question. \"\n", "    \"If you don't know the answer, just say that you don't know. \"\n", "    \"Use three sentences maximum and keep the answer concise.\\n\"\n", "    \"Question: {question} \\n\"\n", "    \"Context: {context}\"\n", ")\n", "\n", "\n", "def generate_answer(state: MessagesState):\n", "    \"\"\"Generate an answer.\"\"\"\n", "    question = state[\"messages\"][0].content\n", "    context = state[\"messages\"][-1].content\n", "    prompt = GENERATE_PROMPT.format(question=question, context=context)\n", "    response = response_model.invoke([{\"role\": \"user\", \"content\": prompt}])\n", "    return {\"messages\": [response]}"]}, {"cell_type": "markdown", "id": "8f59a1fc-fe75-4907-ae86-0dd33c4d9a7f", "metadata": {}, "source": ["2\\. Try it:"]}, {"cell_type": "code", "execution_count": 19, "id": "15d84cb0-b529-49d0-b18e-1d03ff3f7c0d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "<PERSON><PERSON> says that reward hacking can be categorized into two types: environment or goal misspecification, and reward tampering. These categories describe different ways in which an agent might exploit flaws in the reward system. Environment or goal misspecification involves unintended behaviors due to poorly specified objectives, while reward tampering involves directly manipulating the reward signal.\n"]}], "source": ["input = {\n", "    \"messages\": convert_to_messages(\n", "        [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "            },\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": \"\",\n", "                \"tool_calls\": [\n", "                    {\n", "                        \"id\": \"1\",\n", "                        \"name\": \"retrieve_blog_posts\",\n", "                        \"args\": {\"query\": \"types of reward hacking\"},\n", "                    }\n", "                ],\n", "            },\n", "            {\n", "                \"role\": \"tool\",\n", "                \"content\": \"reward hacking can be categorized into two types: environment or goal misspecification, and reward tampering\",\n", "                \"tool_call_id\": \"1\",\n", "            },\n", "        ]\n", "    )\n", "}\n", "\n", "response = generate_answer(input)\n", "response[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "955882ef-7467-48db-ae51-de441f2fc3a7", "metadata": {}, "source": ["## 7. Assemble the graph\n", "\n", "* Start with a `generate_query_or_respond` and determine if we need to call `retriever_tool`\n", "* Route to next step using `tools_condition`:\n", "    * If `generate_query_or_respond` returned `tool_calls`, call `retriever_tool` to retrieve context \n", "    * Otherwise, respond directly to the user\n", "* Grade retrieved document content for relevance to the question (`grade_documents`) and route to next step:\n", "    * If not relevant, rewrite the question using `rewrite_question` and then call `generate_query_or_respond` again\n", "    * If relevant, proceed to `generate_answer` and generate final response using the `ToolMessage` with the retrieved document context"]}, {"cell_type": "code", "execution_count": 20, "id": "8718a37f-83c2-4f16-9850-e61e0f49c3d4", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START, END\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.prebuilt import tools_condition\n", "\n", "workflow = StateGraph(MessagesState)\n", "\n", "# Define the nodes we will cycle between\n", "workflow.add_node(generate_query_or_respond)\n", "workflow.add_node(\"retrieve\", ToolNode([retriever_tool]))\n", "workflow.add_node(rewrite_question)\n", "workflow.add_node(generate_answer)\n", "\n", "workflow.add_edge(START, \"generate_query_or_respond\")\n", "\n", "# Decide whether to retrieve\n", "workflow.add_conditional_edges(\n", "    \"generate_query_or_respond\",\n", "    # Assess LLM decision (call `retriever_tool` tool or respond to the user)\n", "    tools_condition,\n", "    {\n", "        # Translate the condition outputs to nodes in our graph\n", "        \"tools\": \"retrieve\",\n", "        END: END,\n", "    },\n", ")\n", "\n", "# Edges taken after the `action` node is called.\n", "workflow.add_conditional_edges(\n", "    \"retrieve\",\n", "    # Assess agent decision\n", "    grade_documents,\n", ")\n", "workflow.add_edge(\"generate_answer\", END)\n", "workflow.add_edge(\"rewrite_question\", \"generate_query_or_respond\")\n", "\n", "# Compile\n", "graph = workflow.compile()"]}, {"cell_type": "markdown", "id": "fb7c8138-8b01-4858-b9d1-b4bc5f7ebb33", "metadata": {}, "source": ["Visualize the graph:"]}, {"cell_type": "code", "execution_count": 25, "id": "7b5a1d35", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "eb655cfb-d4e4-4f6a-8a0d-01e8611b18ab", "metadata": {}, "source": ["## 8. Run the agentic RAG"]}, {"cell_type": "code", "execution_count": 23, "id": "7649f05a-cb67-490d-b24a-74d41895139a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Update from node generate_query_or_respond\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  retrieve_blog_posts (call_NYu2vq4km9nNNEFqJwefWKu1)\n", " Call ID: call_NYu2vq4km9nNNEFqJwefWKu1\n", "  Args:\n", "    query: types of reward hacking\n", "\n", "\n", "\n", "Update from node retrieve\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: retrieve_blog_posts\n", "\n", "(Note: Some work defines reward tampering as a distinct category of misalignment behavior from reward hacking. But I consider reward hacking as a broader concept here.)\n", "At a high level, reward hacking can be categorized into two types: environment or goal misspecification, and reward tampering.\n", "\n", "Why does <PERSON><PERSON> Hacking Exist?#\n", "\n", "<PERSON> et al. (2022) investigated reward hacking as a function of agent capabilities, including (1) model size, (2) action space resolution, (3) observation space noise, and (4) training time. They also proposed a taxonomy of three types of misspecified proxy rewards:\n", "\n", "Let’s Define Reward Hacking#\n", "Reward shaping in RL is challenging. Reward hacking occurs when an RL agent exploits flaws or ambiguities in the reward function to obtain high rewards without genuinely learning the intended behaviors or completing the task as designed. In recent years, several related concepts have been proposed, all referring to some form of reward hacking:\n", "\n", "\n", "\n", "Update from node generate_answer\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "<PERSON><PERSON> categorizes reward hacking into two types: environment or goal misspecification, and reward tampering. She considers reward hacking as a broad concept that includes both of these categories. Reward hacking occurs when an agent exploits flaws or ambiguities in the reward function to achieve high rewards without performing the intended behaviors.\n", "\n", "\n", "\n"]}], "source": ["for chunk in graph.stream(\n", "    {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"What does <PERSON><PERSON> say about types of reward hacking?\",\n", "            }\n", "        ]\n", "    }\n", "):\n", "    for node, update in chunk.items():\n", "        print(\"Update from node\", node)\n", "        update[\"messages\"][-1].pretty_print()\n", "        print(\"\\n\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}