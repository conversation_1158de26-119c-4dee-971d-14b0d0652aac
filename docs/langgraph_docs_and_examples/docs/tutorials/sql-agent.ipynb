{"cells": [{"cell_type": "markdown", "id": "cb71c4a7-9f99-4f08-be34-b3ba81ac075f", "metadata": {}, "source": ["# Build a SQL agent\n", "\n", "In this tutorial, we will walk through how to build an agent that can answer questions about a SQL database.\n", "\n", "At a high level, the agent will:\n", "\n", "1. Fetch the available tables from the database\n", "2. Decide which tables are relevant to the question\n", "3. <PERSON>tch the schemas for the relevant tables\n", "4. Generate a query based on the question and information from the schemas\n", "5. Double-check the query for common mistakes using an LLM\n", "6. Execute the query and return the results\n", "7. Correct mistakes surfaced by the database engine until the query is successful\n", "8. Formulate a response based on the results\n", "\n", "!!! warning \"Security note\"\n", "\n", "    Building Q&A systems of SQL databases requires executing model-generated SQL queries. There are inherent risks in doing this. Make sure that your database connection permissions are always scoped as narrowly as possible for your agent's needs. This will mitigate though not eliminate the risks of building a model-driven system.\n", "\n", "## 1. <PERSON>up\n", "\n", "Let's first install some dependencies. This tutorial uses SQL database and tool abstractions from [langchain-community](https://python.langchain.com/docs/concepts/architecture/#langchain-community). We will also require a LangChain [chat model](https://python.langchain.com/docs/concepts/chat_models/)."]}, {"cell_type": "code", "execution_count": null, "id": "1b613286-41c3-4ca5-9514-36aac0121a5a", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph langchain_community \"langchain[openai]\""]}, {"cell_type": "markdown", "id": "f5258ad0-39e1-418e-bddb-56e9aab1b43a", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "62886241-caa7-40bd-8867-096a97acaf40", "metadata": {}, "source": ["### Select a LLM\n", "\n", "First we [initialize our LLM](https://python.langchain.com/docs/how_to/chat_models_universal_init/). Any model supporting [tool-calling](https://python.langchain.com/docs/integrations/chat/#featured-providers) should work. We use OpenAI below."]}, {"cell_type": "code", "execution_count": 1, "id": "2b981efc-bcc9-49f4-aa66-4c15a76210a4", "metadata": {}, "outputs": [], "source": ["from langchain.chat_models import init_chat_model\n", "\n", "llm = init_chat_model(\"openai:gpt-4.1\")"]}, {"cell_type": "markdown", "id": "804ca604-7ac4-4229-a3e9-3a9825eeefa4", "metadata": {}, "source": ["### Configure the database\n", "\n", "We will be creating a SQLite database for this tutorial. SQLite is a lightweight database that is easy to set up and use. We will be loading the `chinook` database, which is a sample database that represents a digital media store.\n", "Find more information about the database [here](https://www.sqlitetutorial.net/sqlite-sample-database/).\n", "\n", "For convenience, we have hosted the database (`Chinook.db`) on a public GCS bucket."]}, {"cell_type": "code", "execution_count": null, "id": "b58ba6b7-5f80-4970-bc29-961de0e24fbd", "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "url = \"https://storage.googleapis.com/benchmarks-artifacts/chinook/Chinook.db\"\n", "\n", "response = requests.get(url)\n", "\n", "if response.status_code == 200:\n", "    # Open a local file in binary write mode\n", "    with open(\"Chinook.db\", \"wb\") as file:\n", "        # Write the content of the response (the file) to the local file\n", "        file.write(response.content)\n", "    print(\"File downloaded and saved as Chinook.db\")\n", "else:\n", "    print(f\"Failed to download the file. Status code: {response.status_code}\")"]}, {"cell_type": "markdown", "id": "5ae005fb-e7b0-43f1-95b2-565d19c82610", "metadata": {}, "source": ["We will use a handy SQL database wrapper available in the `langchain_community` package to interact with the database. The wrapper provides a simple interface to execute SQL queries and fetch results:"]}, {"cell_type": "code", "execution_count": 2, "id": "18822c9b-6df2-4a5c-a12d-046d2b2131c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dialect: sqlite\n", "Available tables: ['Album', 'Artist', 'Customer', 'Employee', 'Genre', 'Invoice', 'InvoiceLine', 'MediaType', 'Playlist', 'PlaylistTrack', 'Track']\n", "Sample output: [(1, 'AC/DC'), (2, 'Accept'), (3, '<PERSON>smith'), (4, '<PERSON><PERSON>'), (5, 'Alice In Chains')]\n"]}], "source": ["from langchain_community.utilities import SQLDatabase\n", "\n", "db = SQLDatabase.from_uri(\"sqlite:///Chinook.db\")\n", "\n", "print(f\"Dialect: {db.dialect}\")\n", "print(f\"Available tables: {db.get_usable_table_names()}\")\n", "print(f'Sample output: {db.run(\"SELECT * FROM Artist LIMIT 5;\")}')"]}, {"cell_type": "markdown", "id": "3bf47b6c-30dd-405f-8a03-21327af3c764", "metadata": {}, "source": ["### Tools for database interactions\n", "\n", "`langchain-community` implements some built-in tools for interacting with our `SQLDatabase`, including tools for listing tables, reading table schemas, and checking and running queries:"]}, {"cell_type": "code", "execution_count": 3, "id": "aab9e00a-aa28-494a-a95a-a6638ea03bd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sql_db_query: Input to this tool is a detailed and correct SQL query, output is a result from the database. If the query is not correct, an error message will be returned. If an error is returned, rewrite the query, check the query, and try again. If you encounter an issue with Unknown column 'xxxx' in 'field list', use sql_db_schema to query the correct table fields.\n", "\n", "sql_db_schema: Input to this tool is a comma-separated list of tables, output is the schema and sample rows for those tables. Be sure that the tables actually exist by calling sql_db_list_tables first! Example Input: table1, table2, table3\n", "\n", "sql_db_list_tables: Input is an empty string, output is a comma-separated list of tables in the database.\n", "\n", "sql_db_query_checker: Use this tool to double check if your query is correct before executing it. Always use this tool before executing a query with sql_db_query!\n", "\n"]}], "source": ["from langchain_community.agent_toolkits import SQLDatabaseToolkit\n", "\n", "toolkit = SQLDatabaseToolkit(db=db, llm=llm)\n", "\n", "tools = toolkit.get_tools()\n", "\n", "for tool in tools:\n", "    print(f\"{tool.name}: {tool.description}\\n\")"]}, {"cell_type": "markdown", "id": "6baf93d3-afd5-484e-9bf7-3ff20401e06f", "metadata": {}, "source": ["## 2. Using a prebuilt agent\n", "\n", "Given these tools, we can initialize a pre-built agent in a single line. To customize our agents behavior, we write a descriptive system prompt."]}, {"cell_type": "code", "execution_count": 4, "id": "f6b9f10f-c6cb-43bc-b671-79aadc71280b", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "\n", "system_prompt = \"\"\"\n", "You are an agent designed to interact with a SQL database.\n", "Given an input question, create a syntactically correct {dialect} query to run,\n", "then look at the results of the query and return the answer. Unless the user\n", "specifies a specific number of examples they wish to obtain, always limit your\n", "query to at most {top_k} results.\n", "\n", "You can order the results by a relevant column to return the most interesting\n", "examples in the database. Never query for all the columns from a specific table,\n", "only ask for the relevant columns given the question.\n", "\n", "You MUST double check your query before executing it. If you get an error while\n", "executing a query, rewrite the query and try again.\n", "\n", "DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the\n", "database.\n", "\n", "To start you should ALWAYS look at the tables in the database to see what you\n", "can query. Do NOT skip this step.\n", "\n", "Then you should query the schema of the most relevant tables.\n", "\"\"\".format(\n", "    dialect=db.dialect,\n", "    top_k=5,\n", ")\n", "\n", "agent = create_react_agent(\n", "    llm,\n", "    tools,\n", "    prompt=system_prompt,\n", ")"]}, {"cell_type": "markdown", "id": "78b1c7cf-9a0f-48c3-baec-be8101bc3c77", "metadata": {}, "source": ["!!! note\n", "\n", "    This system prompt includes a number of instructions, such as always running specific tools before or after others. In the [next section](#customizing-the-agent), we will enforce these behaviors through the graph's structure, providing us a greater degree of control and allowing us to simplify the prompt.\n", "\n", "\n", "Let's run this agent on a sample query and observe its behavior:"]}, {"cell_type": "code", "execution_count": 5, "id": "9317c234-6251-4723-a335-7fee6d11ac2b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Which sales agent made the most in sales in 2009?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_list_tables (call_0MfnhUYmRNVe03m6fYClaoa0)\n", " Call ID: call_0MfnhUYmRNVe03m6fYClaoa0\n", "  Args:\n", "    tool_input:\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_list_tables\n", "\n", "Album, Artist, Customer, Employee, Genre, Invoice, InvoiceLine, MediaType, Playlist, PlaylistTrack, Track\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_schema (call_XwI6uES0HeZ2xCRxkkdWzB3I)\n", " Call ID: call_XwI6uES0HeZ2xCRxkkdWzB3I\n", "  Args:\n", "    table_names: Employee, Invoice, InvoiceLine\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_schema\n", "\n", "\n", "CREATE TABLE \"Employee\" (\n", "\t\"EmployeeId\" INTEGER NOT NULL, \n", "\t\"LastName\" NVARCHAR(20) NOT NULL, \n", "\t\"FirstName\" NVARCHAR(20) NOT NULL, \n", "\t\"Title\" NVARCHAR(30), \n", "\t\"ReportsTo\" INTEGER, \n", "\t\"BirthDate\" DATETIME, \n", "\t\"HireDate\" DATETIME, \n", "\t\"Address\" NVARCHAR(70), \n", "\t\"City\" NVARCHAR(40), \n", "\t\"State\" NVARCHAR(40), \n", "\t\"Country\" NVARCHAR(40), \n", "\t\"PostalCode\" NVARCHAR(10), \n", "\t\"Phone\" NVARCHAR(24), \n", "\t\"Fax\" NVARCHAR(24), \n", "\t\"Email\" NVARCHAR(60), \n", "\tPRIMARY KEY (\"EmployeeId\"), \n", "\tFOREIGN KEY(\"ReportsTo\") REFERENCES \"Employee\" (\"EmployeeId\")\n", ")\n", "\n", "/*\n", "3 rows from Employee table:\n", "EmployeeId\tLastName\tFirstName\tTitle\tReportsTo\tBirthDate\tHireDate\tAddress\tCity\tState\tCountry\tPostalCode\tPhone\tFax\tEmail\n", "1\t<PERSON>\tGeneral Manager\tNone\t1962-02-18 00:00:00\t2002-08-14 00:00:00\t11120 Jasper Ave NW\tEdmonton\tAB\tCanada\tT5K 2N1\t*****************\t*****************\t<EMAIL>\n", "2\t<PERSON>\tSales Manager\t1\t1958-12-08 00:00:00\t2002-05-01 00:00:00\t825 8 Ave SW\tCalgary\tAB\tCanada\tT2P 2T3\t*****************\t*****************\t<EMAIL>\n", "3\t<PERSON>\tJane\tSales Support Agent\t2\t1973-08-29 00:00:00\t2002-04-01 00:00:00\t1111 6 Ave SW\tCalgary\tAB\tCanada\tT2P 5M5\t*****************\t*****************\t<EMAIL>\n", "*/\n", "\n", "\n", "CREATE TABLE \"Invoice\" (\n", "\t\"InvoiceId\" INTEGER NOT NULL, \n", "\t\"CustomerId\" INTEGER NOT NULL, \n", "\t\"InvoiceDate\" DATETIME NOT NULL, \n", "\t\"BillingAddress\" NVARCHAR(70), \n", "\t\"BillingCity\" NVARCHAR(40), \n", "\t\"BillingState\" NVARCHAR(40), \n", "\t\"BillingCountry\" NVARCHAR(40), \n", "\t\"BillingPostalCode\" NVARCHAR(10), \n", "\t\"Total\" NUMERIC(10, 2) NOT NULL, \n", "\tPRIMARY KEY (\"InvoiceId\"), \n", "\tFOREIGN KEY(\"CustomerId\") REFERENCES \"Customer\" (\"CustomerId\")\n", ")\n", "\n", "/*\n", "3 rows from Invoice table:\n", "InvoiceId\tCustomerId\tInvoiceDate\tBillingAddress\tBillingCity\tBillingState\tBillingCountry\tBillingPostalCode\tTotal\n", "1\t2\t2009-01-01 00:00:00\tTheodor-Heuss-Straße 34\tStuttgart\tNone\tGermany\t70174\t1.98\n", "2\t4\t2009-01-02 00:00:00\tUllevålsveien 14\tOslo\tNone\tNorway\t0171\t3.96\n", "3\t8\t2009-01-03 00:00:00\tGrétrystraat 63\tBrussels\tNone\tBelgium\t1000\t5.94\n", "*/\n", "\n", "\n", "CREATE TABLE \"InvoiceLine\" (\n", "\t\"InvoiceLineId\" INTEGER NOT NULL, \n", "\t\"InvoiceId\" INTEGER NOT NULL, \n", "\t\"TrackId\" INTEGER NOT NULL, \n", "\t\"UnitPrice\" NUMERIC(10, 2) NOT NULL, \n", "\t\"Quantity\" INTEGER NOT NULL, \n", "\tPRIMARY KEY (\"InvoiceLineId\"), \n", "\tFOREIGN KEY(\"TrackId\") REFERENCES \"Track\" (\"TrackId\"), \n", "\tFOREIGN KEY(\"InvoiceId\") REFERENCES \"Invoice\" (\"InvoiceId\")\n", ")\n", "\n", "/*\n", "3 rows from InvoiceLine table:\n", "InvoiceLineId\tInvoiceId\tTrackId\tUnitPrice\tQuantity\n", "1\t1\t2\t0.99\t1\n", "2\t1\t4\t0.99\t1\n", "3\t2\t6\t0.99\t1\n", "*/\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_schema (call_dHE5FKSycFhYIkdPKtpEk9rM)\n", " Call ID: call_dHE5FKSycFhYIkdPKtpEk9rM\n", "  Args:\n", "    table_names: Customer\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_schema\n", "\n", "\n", "CREATE TABLE \"Customer\" (\n", "\t\"CustomerId\" INTEGER NOT NULL, \n", "\t\"FirstName\" NVARCHAR(40) NOT NULL, \n", "\t\"LastName\" NVARCHAR(20) NOT NULL, \n", "\t\"Company\" NVARCHAR(80), \n", "\t\"Address\" NVARCHAR(70), \n", "\t\"City\" NVARCHAR(40), \n", "\t\"State\" NVARCHAR(40), \n", "\t\"Country\" NVARCHAR(40), \n", "\t\"PostalCode\" NVARCHAR(10), \n", "\t\"Phone\" NVARCHAR(24), \n", "\t\"Fax\" NVARCHAR(24), \n", "\t\"Email\" NVARCHAR(60) NOT NULL, \n", "\t\"SupportRepId\" INTEGER, \n", "\tPRIMARY KEY (\"CustomerId\"), \n", "\tFOREIGN KEY(\"SupportRepId\") REFERENCES \"Employee\" (\"EmployeeId\")\n", ")\n", "\n", "/*\n", "3 rows from Customer table:\n", "CustomerId\tFirstName\tLastName\tCompany\tAddress\tCity\tState\tCountry\tPostalCode\tPhone\tFax\tEmail\tSupportRepId\n", "1\tLuís\tGonçalves\tEmbraer - Empresa Brasileira de Aeronáutica S.A.\tAv. <PERSON><PERSON> Faria Lima, 2170\tSão José dos Campos\tSP\tBrazil\t12227-000\t+55 (12) 3923-5555\t+55 (12) 3923-5566\t<EMAIL>\t3\n", "2\t<PERSON><PERSON>\tNone\tTheodor-<PERSON><PERSON>-Straße 34\tStuttgart\tNone\tGermany\t70174\t+49 0711 2842222\tNone\t<PERSON><PERSON><PERSON><PERSON>@surfeu.de\t5\n", "3\t<PERSON>\tNone\t1498 rue Bélanger\tMontréal\tQC\tCanada\tH2G 1A7\t*****************\tNone\t<EMAIL>\t3\n", "*/\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_query_checker (call_QPtWY5cUZWgWhr13Kz6eBJkU)\n", " Call ID: call_QPtWY5cUZWgWhr13Kz6eBJkU\n", "  Args:\n", "    query: SELECT e.Employee<PERSON>d, e.FirstName, e.<PERSON>, SUM(i.Total) as TotalSales\n", "FROM Employee e\n", "JOIN Customer c ON e.EmployeeId = c.SupportRepId\n", "JOIN Invoice i ON c.CustomerId = i.CustomerId\n", "WHERE i.InvoiceDate BETWEEN '2009-01-01' AND '2009-12-31'\n", "GROUP BY e.<PERSON>, e.<PERSON>, e.<PERSON><PERSON>ame\n", "ORDER BY TotalSales DESC\n", "LIMIT 1;\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_query_checker\n", "\n", "```sql\n", "SELECT e.Employee<PERSON>d, e.<PERSON>Name, e.LastName, SUM(i.Total) as TotalSales\n", "FROM Employee e\n", "JOIN Customer c ON e.EmployeeId = c.SupportRepId\n", "JOIN Invoice i ON c.CustomerId = i.CustomerId\n", "WHERE i.InvoiceDate BETWEEN '2009-01-01' AND '2009-12-31'\n", "GROUP BY e.<PERSON>, e.<PERSON>, e.<PERSON><PERSON>ame\n", "ORDER BY TotalSales DESC\n", "LIMIT 1;\n", "```\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_query (call_X747YMElSeY5D3CfhE6KzLUj)\n", " Call ID: call_X747YMElSeY5D3CfhE6KzLUj\n", "  Args:\n", "    query: SELECT e.Employee<PERSON>d, e.FirstName, e.<PERSON>, SUM(i.Total) as TotalSales\n", "FROM Employee e\n", "JOIN Customer c ON e.EmployeeId = c.SupportRepId\n", "JOIN Invoice i ON c.CustomerId = i.CustomerId\n", "WHERE i.InvoiceDate BETWEEN '2009-01-01' AND '2009-12-31'\n", "GROUP BY e.<PERSON>, e.<PERSON>, e.<PERSON><PERSON>ame\n", "ORDER BY TotalSales DESC\n", "LIMIT 1;\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_query\n", "\n", "[(5, '<PERSON>', '<PERSON>', 164.34)]\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The sales agent who made the most in sales in 2009 was <PERSON>, with total sales of 164.34.\n"]}], "source": ["question = \"Which sales agent made the most in sales in 2009?\"\n", "\n", "for step in agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": question}]},\n", "    stream_mode=\"values\",\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "6b2afc54-0a26-40f8-bca2-01cbb2fc3443", "metadata": {}, "source": ["This worked well enough: the agent correctly listed the tables, obtained the schemas, wrote a query, checked the query, and ran it to inform its final response.\n", "\n", "!!! tip\n", "\n", "    You can inspect all aspects of the above run, including steps taken, tools invoked, what prompts were seen by the LLM, and more in the [LangSmith trace](https://smith.langchain.com/public/bd594960-73e3-474b-b6f2-db039d7c713a/r)."]}, {"cell_type": "markdown", "id": "2ed79712-ca6b-47dc-9bc9-7cb6fc50a8aa", "metadata": {}, "source": ["## 3. Customizing the agent\n", "\n", "The prebuilt agent lets us get started quickly, but at each step the agent has access to the full set of tools. Above, we relied on the system prompt to constrain its behavior— for example, we instructed the agent to always start with the \"list tables\" tool, and to always run a query-checker tool before executing the query.\n", "\n", "We can enforce a higher degree of control in LangGraph by customizing the agent. Below, we implement a simple ReAct-agent setup, with dedicated nodes for specific tool-calls. We will use the same [state](../../concepts/low_level/#state) as the pre-built agent.\n", "\n", "We construct dedicated nodes for the following steps:\n", "\n", "- Listing DB tables\n", "- Calling the \"get schema\" tool\n", "- Generating a query\n", "- Checking the query\n", "\n", "Putting these steps in dedicated nodes lets us (1) force tool-calls when needed, and (2) customize the prompts associated with each step."]}, {"cell_type": "code", "execution_count": 7, "id": "992f6ce6-f932-4f44-994c-c0ba5032dbd1", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "from langchain_core.messages import AIMessage\n", "from langchain_core.runnables import RunnableConfig\n", "from langgraph.graph import END, START, MessagesState, StateGraph\n", "from langgraph.prebuilt import ToolNode\n", "\n", "\n", "get_schema_tool = next(tool for tool in tools if tool.name == \"sql_db_schema\")\n", "get_schema_node = ToolNode([get_schema_tool], name=\"get_schema\")\n", "\n", "run_query_tool = next(tool for tool in tools if tool.name == \"sql_db_query\")\n", "run_query_node = ToolNode([run_query_tool], name=\"run_query\")\n", "\n", "\n", "# Example: create a predetermined tool call\n", "def list_tables(state: MessagesState):\n", "    tool_call = {\n", "        \"name\": \"sql_db_list_tables\",\n", "        \"args\": {},\n", "        \"id\": \"abc123\",\n", "        \"type\": \"tool_call\",\n", "    }\n", "    tool_call_message = AIMessage(content=\"\", tool_calls=[tool_call])\n", "\n", "    list_tables_tool = next(tool for tool in tools if tool.name == \"sql_db_list_tables\")\n", "    tool_message = list_tables_tool.invoke(tool_call)\n", "    response = AIMessage(f\"Available tables: {tool_message.content}\")\n", "\n", "    return {\"messages\": [tool_call_message, tool_message, response]}\n", "\n", "\n", "# Example: force a model to create a tool call\n", "def call_get_schema(state: MessagesState):\n", "    # Note that <PERSON><PERSON><PERSON><PERSON> enforces that all models accept `tool_choice=\"any\"`\n", "    # as well as `tool_choice=<string name of tool>`.\n", "    llm_with_tools = llm.bind_tools([get_schema_tool], tool_choice=\"any\")\n", "    response = llm_with_tools.invoke(state[\"messages\"])\n", "\n", "    return {\"messages\": [response]}\n", "\n", "\n", "generate_query_system_prompt = \"\"\"\n", "You are an agent designed to interact with a SQL database.\n", "Given an input question, create a syntactically correct {dialect} query to run,\n", "then look at the results of the query and return the answer. Unless the user\n", "specifies a specific number of examples they wish to obtain, always limit your\n", "query to at most {top_k} results.\n", "\n", "You can order the results by a relevant column to return the most interesting\n", "examples in the database. Never query for all the columns from a specific table,\n", "only ask for the relevant columns given the question.\n", "\n", "DO NOT make any DML statements (INSERT, UPDATE, DELETE, DROP etc.) to the database.\n", "\"\"\".format(\n", "    dialect=db.dialect,\n", "    top_k=5,\n", ")\n", "\n", "\n", "def generate_query(state: MessagesState):\n", "    system_message = {\n", "        \"role\": \"system\",\n", "        \"content\": generate_query_system_prompt,\n", "    }\n", "    # We do not force a tool call here, to allow the model to\n", "    # respond naturally when it obtains the solution.\n", "    llm_with_tools = llm.bind_tools([run_query_tool])\n", "    response = llm_with_tools.invoke([system_message] + state[\"messages\"])\n", "\n", "    return {\"messages\": [response]}\n", "\n", "\n", "check_query_system_prompt = \"\"\"\n", "You are a SQL expert with a strong attention to detail.\n", "Double check the {dialect} query for common mistakes, including:\n", "- Using NOT IN with NULL values\n", "- Using UNION when UNION ALL should have been used\n", "- Using BETWEEN for exclusive ranges\n", "- Data type mismatch in predicates\n", "- Properly quoting identifiers\n", "- Using the correct number of arguments for functions\n", "- Casting to the correct data type\n", "- Using the proper columns for joins\n", "\n", "If there are any of the above mistakes, rewrite the query. If there are no mistakes,\n", "just reproduce the original query.\n", "\n", "You will call the appropriate tool to execute the query after running this check.\n", "\"\"\".format(dialect=db.dialect)\n", "\n", "\n", "def check_query(state: MessagesState):\n", "    system_message = {\n", "        \"role\": \"system\",\n", "        \"content\": check_query_system_prompt,\n", "    }\n", "\n", "    # Generate an artificial user message to check\n", "    tool_call = state[\"messages\"][-1].tool_calls[0]\n", "    user_message = {\"role\": \"user\", \"content\": tool_call[\"args\"][\"query\"]}\n", "    llm_with_tools = llm.bind_tools([run_query_tool], tool_choice=\"any\")\n", "    response = llm_with_tools.invoke([system_message, user_message])\n", "    response.id = state[\"messages\"][-1].id\n", "\n", "    return {\"messages\": [response]}"]}, {"cell_type": "markdown", "id": "cd23a432-09c1-428c-88a7-6b4d1fe55577", "metadata": {}, "source": ["Finally, we assemble these steps into a workflow using the Graph API. We define a [conditional edge](../../concepts/low_level/#conditional-edges) at the query generation step that will route to the query checker if a query is generated, or end if there are no tool calls present, such that the LLM has delivered a response to the query."]}, {"cell_type": "code", "execution_count": 8, "id": "8b3ce09d-0eec-456d-8447-d04f79ccfc23", "metadata": {}, "outputs": [], "source": ["def should_continue(state: MessagesState) -> Literal[END, \"check_query\"]:\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    if not last_message.tool_calls:\n", "        return END\n", "    else:\n", "        return \"check_query\"\n", "\n", "\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(list_tables)\n", "builder.add_node(call_get_schema)\n", "builder.add_node(get_schema_node, \"get_schema\")\n", "builder.add_node(generate_query)\n", "builder.add_node(check_query)\n", "builder.add_node(run_query_node, \"run_query\")\n", "\n", "builder.add_edge(START, \"list_tables\")\n", "builder.add_edge(\"list_tables\", \"call_get_schema\")\n", "builder.add_edge(\"call_get_schema\", \"get_schema\")\n", "builder.add_edge(\"get_schema\", \"generate_query\")\n", "builder.add_conditional_edges(\n", "    \"generate_query\",\n", "    should_continue,\n", ")\n", "builder.add_edge(\"check_query\", \"run_query\")\n", "builder.add_edge(\"run_query\", \"generate_query\")\n", "\n", "agent = builder.compile()"]}, {"cell_type": "markdown", "id": "c377e9dd-92a7-45d4-91bf-408a6f2af8ca", "metadata": {}, "source": ["We visualize the application below:"]}, {"cell_type": "code", "execution_count": 9, "id": "f728ed5f-7bcd-489e-9174-4e4725962335", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAATQAAAKFCAIAAADNnYKFAAAQAElEQVR4nOzdBXhTVx8G8BNPXalRp0DRFofi7jrcPtyHu8PQDdkYDttwHzLGhsPwbTBcCoVCoS7UNWm/f3u3rIO2tFA5ad7fA31uruWmvW+OJfdK09LSGADwR8oAgEsIJwCnEE4ATiGcAJxCOAE4hXACcArhhI8U9DIpPloVF61Sq9KSElIZ9xR6YrFEZGAiNTCW2DgrGfdEGOeEPHn8e7Tvwzj651LegImYgbHU1FqenKBm3FPoSd6GJKe/m6SkvXoc71xe36WiYfnaxiIR4xPCCbl197fI309GlKps6FLBwLmigVjMtNpLeot5EPfiQZxnI9NqTc0YfxBO+LDgV4m/bguiWHq1t5BIeS1oPta14+EPr0e1GmDrUEaP8QThhA94dCOazt22g+30jSWsmEqKTz29O8ihjL5nQ1PGDYQTcvL8biw1z5r0tGI64PKRMHNbeYXaxowPCCdk68/TEVGhqmZ9dCKZgt8OhUpkonodLRkHtLxRDwXmxf240NdJOpVM0rBricQ49eM/ohkHEE7IAhWY3jej2wy2ZbqnWW/rN88SQt8ksaKGcEIWLh8Lda/JS9Or8FWoY3LpSCgraggnvCvQN5GqdjSYyXSVnatSrhC/ehTPihTCCe+isZP6nbjoESlC9TqWeHyziFueCCf8R2Jcqu+DWGunQv3o6YEDB+bPn8/ybtq0acePH2cFwMxaFuKXFBWWwooOwgn/4fsw1rnQK7SPHz9mH+WjN8wNqtj7PoxjRQfjnPAfFw6EuFQydC6nzwrA7du3161b5+Pjo1ary5QpM3r06KpVqw4bNuyvv/4SVti9e3fZsmVPnjy5c+dOPz8/uVxeuXLlSZMm2dvbs4xyUiQSOTs779q1a+nSpRMmTBC2MjQ0vHjxIstv1PZ+cDWqeV9rVkRQcsJ/0BlpZFogXyRMSEgYP368q6vrDz/8sH379tKlS48dOzY6OnrVqlXu7u4tWrQ4e/asm5vbw4cPZ8+eXbduXcrnmjVraKspU6YIe5DJZBTsJ0+e0PxKlSr98ssvNJOWHjt2jBUAIzPpG58EVnTwfU74j/holYFxgZwVQUFBcXFxbdq0cXFxoYeTJ09u3rw5lY1KpVIqldKEqWn651qdnJwolhRdmkkPe/fuPXHixIiICHNzc3r45s2b7777zsTEhKaTktKHIvX19YWH+Y5+D/TbYEUH4YR/papZcmKq0qBA6lOOjo4UPCoVu3btWrt2baq+VqtW7f3VqI7q7++/du3a169fJyYmpqSkd8lQASuEk/ZQQFF8n0jMFPqShFi1nmHRfOIf1Vr4V6o6Tc+ooN6vJRLJ1q1bmzVrduTIkb59+7Zv3/7EiRPvr3b69Onp06dXrFiR6q579uyZNWtW5qUUXVaI9AzEqUX3NXKEE/4llYtSklKTC+yaI2ZmZtTspCYijZ3UrFlz3rx573e3UnSrV68+cuRI6vixtLSkwpMVnbchKQYmRfZFOYQT/oPOxbiCaWhRZVXTp0rdQjNnzhSLxc+fPxfmaEYNkpOThcangHpuMy99X8ENN8RFq4v2K6wIJ/yHnat+fEyB1OSoQ2jq1Kk0CvLy5ctXr15RFZfCSZ2utMjIyMg7Q2RkJFVob9y48eDBg8DAQBovocKTVnj06NH7RagiAw3D0IYqVf6/oVBvkEPpAhlSyiXJx30yA4qr2Lcq/+cJzuXz/3MIdhl+/PHHbdu2Uc02Pj6e2pY0jEmLqI+H2p+HDx+uUqUKjak8e/Zs8+bNNFJCPUY0mHnv3r39+/dTLZdGPmNjYzt27KjZZ2pqKlWDT506RZ1MFFSWr+5fiaIOW7tSRXbtEnwIAf4jNlJ16Js3A+Y5M52390u/Fv1sLGzlrIigWgv/YWgqtXVWRgQV5WdKeRAXpTYylxVhMhnGOeF9ZaoZXf85rO2QbL9pPWTIEB8fn/fnq9XpjVUaMslyK6rKFtAQ5Z07d6gTOMtFdEjZHQ85d+5cdkvpN+DmUajDNu9DtRayQDXbuh0sbV2y/m5KaGio8NmAdwgf2cmu7WdjYyMumGvd0vOGh4dnt0gmk2X3vNQGznJ+RFDyr9uC+kx3ZEUK4YQsBPkmPvo9Wkcuuve+************************************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********************************************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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langchain_core.runnables.graph import CurveStyle, MermaidDrawMethod, NodeStyles\n", "\n", "display(Image(agent.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "977173cf-8f8d-416e-a122-aebff115f29a", "metadata": {}, "source": ["We can now invoke the graph exactly as before:"]}, {"cell_type": "code", "execution_count": 10, "id": "6c800a77-2359-4011-ab0c-dad22ed333b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Which sales agent made the most in sales in 2009?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Available tables: Album, Artist, Customer, Employee, Genre, Invoice, InvoiceLine, MediaType, Playlist, PlaylistTrack, Track\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_schema (call_rMs3sF1HVcAGggGf89ZNvtLE)\n", " Call ID: call_rMs3sF1HVcAGggGf89ZNvtLE\n", "  Args:\n", "    table_names: <PERSON><PERSON><PERSON><PERSON>,Invoice,Customer\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_schema\n", "\n", "\n", "CREATE TABLE \"Customer\" (\n", "\t\"CustomerId\" INTEGER NOT NULL, \n", "\t\"FirstName\" NVARCHAR(40) NOT NULL, \n", "\t\"LastName\" NVARCHAR(20) NOT NULL, \n", "\t\"Company\" NVARCHAR(80), \n", "\t\"Address\" NVARCHAR(70), \n", "\t\"City\" NVARCHAR(40), \n", "\t\"State\" NVARCHAR(40), \n", "\t\"Country\" NVARCHAR(40), \n", "\t\"PostalCode\" NVARCHAR(10), \n", "\t\"Phone\" NVARCHAR(24), \n", "\t\"Fax\" NVARCHAR(24), \n", "\t\"Email\" NVARCHAR(60) NOT NULL, \n", "\t\"SupportRepId\" INTEGER, \n", "\tPRIMARY KEY (\"CustomerId\"), \n", "\tFOREIGN KEY(\"SupportRepId\") REFERENCES \"Employee\" (\"EmployeeId\")\n", ")\n", "\n", "/*\n", "3 rows from Customer table:\n", "CustomerId\tFirstName\tLastName\tCompany\tAddress\tCity\tState\tCountry\tPostalCode\tPhone\tFax\tEmail\tSupportRepId\n", "1\tLuís\tGonçalves\tEmbraer - Empresa Brasileira de Aeronáutica S.A.\tAv. <PERSON><PERSON> Faria Lima, 2170\tSão José dos Campos\tSP\tBrazil\t12227-000\t+55 (12) 3923-5555\t+55 (12) 3923-5566\t<EMAIL>\t3\n", "2\t<PERSON><PERSON>\tNone\tTheodor-<PERSON><PERSON>-Straße 34\tStuttgart\tNone\tGermany\t70174\t+49 0711 2842222\tNone\t<PERSON><PERSON><PERSON><PERSON>@surfeu.de\t5\n", "3\t<PERSON>\tNone\t1498 rue Bélanger\tMontréal\tQC\tCanada\tH2G 1A7\t*****************\tNone\t<EMAIL>\t3\n", "*/\n", "\n", "\n", "CREATE TABLE \"Employee\" (\n", "\t\"EmployeeId\" INTEGER NOT NULL, \n", "\t\"LastName\" NVARCHAR(20) NOT NULL, \n", "\t\"FirstName\" NVARCHAR(20) NOT NULL, \n", "\t\"Title\" NVARCHAR(30), \n", "\t\"ReportsTo\" INTEGER, \n", "\t\"BirthDate\" DATETIME, \n", "\t\"HireDate\" DATETIME, \n", "\t\"Address\" NVARCHAR(70), \n", "\t\"City\" NVARCHAR(40), \n", "\t\"State\" NVARCHAR(40), \n", "\t\"Country\" NVARCHAR(40), \n", "\t\"PostalCode\" NVARCHAR(10), \n", "\t\"Phone\" NVARCHAR(24), \n", "\t\"Fax\" NVARCHAR(24), \n", "\t\"Email\" NVARCHAR(60), \n", "\tPRIMARY KEY (\"EmployeeId\"), \n", "\tFOREIGN KEY(\"ReportsTo\") REFERENCES \"Employee\" (\"EmployeeId\")\n", ")\n", "\n", "/*\n", "3 rows from Employee table:\n", "EmployeeId\tLastName\tFirstName\tTitle\tReportsTo\tBirthDate\tHireDate\tAddress\tCity\tState\tCountry\tPostalCode\tPhone\tFax\tEmail\n", "1\t<PERSON>\tGeneral Manager\tNone\t1962-02-18 00:00:00\t2002-08-14 00:00:00\t11120 Jasper Ave NW\tEdmonton\tAB\tCanada\tT5K 2N1\t*****************\t*****************\t<EMAIL>\n", "2\t<PERSON>\tSales Manager\t1\t1958-12-08 00:00:00\t2002-05-01 00:00:00\t825 8 Ave SW\tCalgary\tAB\tCanada\tT2P 2T3\t*****************\t*****************\t<EMAIL>\n", "3\t<PERSON>\tJane\tSales Support Agent\t2\t1973-08-29 00:00:00\t2002-04-01 00:00:00\t1111 6 Ave SW\tCalgary\tAB\tCanada\tT2P 5M5\t*****************\t*****************\t<EMAIL>\n", "*/\n", "\n", "\n", "CREATE TABLE \"Invoice\" (\n", "\t\"InvoiceId\" INTEGER NOT NULL, \n", "\t\"CustomerId\" INTEGER NOT NULL, \n", "\t\"InvoiceDate\" DATETIME NOT NULL, \n", "\t\"BillingAddress\" NVARCHAR(70), \n", "\t\"BillingCity\" NVARCHAR(40), \n", "\t\"BillingState\" NVARCHAR(40), \n", "\t\"BillingCountry\" NVARCHAR(40), \n", "\t\"BillingPostalCode\" NVARCHAR(10), \n", "\t\"Total\" NUMERIC(10, 2) NOT NULL, \n", "\tPRIMARY KEY (\"InvoiceId\"), \n", "\tFOREIGN KEY(\"CustomerId\") REFERENCES \"Customer\" (\"CustomerId\")\n", ")\n", "\n", "/*\n", "3 rows from Invoice table:\n", "InvoiceId\tCustomerId\tInvoiceDate\tBillingAddress\tBillingCity\tBillingState\tBillingCountry\tBillingPostalCode\tTotal\n", "1\t2\t2009-01-01 00:00:00\tTheodor-Heuss-Straße 34\tStuttgart\tNone\tGermany\t70174\t1.98\n", "2\t4\t2009-01-02 00:00:00\tUllevålsveien 14\tOslo\tNone\tNorway\t0171\t3.96\n", "3\t8\t2009-01-03 00:00:00\tGrétrystraat 63\tBrussels\tNone\tBelgium\t1000\t5.94\n", "*/\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_query (call_TQyoJSV78xEWHqc69PiYHjdS)\n", " Call ID: call_TQyoJSV78xEWHqc69PiYHjdS\n", "  Args:\n", "    query: SELECT e.FirstName, e.LastN<PERSON>, SUM(i.Total) as TotalSales\n", "FROM Employee e\n", "JOIN Customer c ON e.EmployeeId = c.SupportRepId\n", "JOIN Invoice i ON c.CustomerId = i.CustomerId\n", "WHERE strftime('%Y', i.InvoiceDate) = '2009'\n", "GROUP BY e.EmployeeId\n", "ORDER BY TotalSales DESC\n", "LIMIT 1;\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  sql_db_query (call_tSQOuxc7iQ1jvLzXSofCqK4V)\n", " Call ID: call_tSQOuxc7iQ1jvLzXSofCqK4V\n", "  Args:\n", "    query: SELECT e.FirstName, e.LastN<PERSON>, SUM(i.Total) as TotalSales\n", "FROM Employee e\n", "JOIN Customer c ON e.EmployeeId = c.SupportRepId\n", "JOIN Invoice i ON c.CustomerId = i.CustomerId\n", "WHERE strftime('%Y', i.InvoiceDate) = '2009'\n", "GROUP BY e.EmployeeId\n", "ORDER BY TotalSales DESC\n", "LIMIT 1;\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: sql_db_query\n", "\n", "[('<PERSON>', '<PERSON>', 164.34)]\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The sales agent who made the most in sales in 2009 was <PERSON>, with total sales of 164.34.\n"]}], "source": ["question = \"Which sales agent made the most in sales in 2009?\"\n", "\n", "for step in agent.stream(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": question}]},\n", "    stream_mode=\"values\",\n", "):\n", "    step[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "fc3d8130-15c3-4c22-bb17-eb82ad41586e", "metadata": {}, "source": ["!!! tip\n", "\n", "    See [Lang<PERSON><PERSON> trace](https://smith.langchain.com/public/36a9ebbb-7cc4-4c54-b4db-cb12e124a649/r) for the above run."]}, {"cell_type": "markdown", "id": "838b3acf-3186-4aa1-b211-cf136e95c39d", "metadata": {}, "source": ["## Next steps\n", "\n", "Check out [this guide](https://docs.smith.langchain.com/evaluation/how_to_guides/langgraph) for evaluating LangGraph applications, including SQL agents like this one, using LangSmith."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}