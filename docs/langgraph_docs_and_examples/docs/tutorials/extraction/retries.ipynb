{"cells": [{"cell_type": "markdown", "id": "e327e9bd-effc-4bee-a875-1c383c17f43d", "metadata": {}, "source": ["# Complex data extraction with function calling\n", "\n", "Function calling is a core primitive for integrating LLMs within your software stack. We use it throughout the LangGraph docs, since developing with function calling (aka tool usage) tends to be much more stress-free than the traditional way of writing custom string parsers.\n", "\n", "However, even GPT-4, Opus, and other powerful models still struggle with complex functions, especially if your schema involves any nesting or if you have more advanced data validation rules.\n", "\n", "There are three basic ways to increase reliability: better prompting, constrained decoding, and **validation with re-prompting**.\n", "\n", "We will cover two approaches to the last technique here, since it is generally applicable across any LLM that supports tool calling.\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "0ada5e8f-3f2f-459e-83aa-6cd8861770dd", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langchain-anthropic langgraph"]}, {"cell_type": "code", "execution_count": 2, "id": "c0acb818-b6fd-48ab-97e6-fc2de2d03e87", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "f07bc7a6", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "ba53b3c0", "metadata": {}, "source": ["## Regular Extraction with Retries\n", "\n", "Both examples here invoke a simple looping graph that takes following approach:\n", "1. Prompt the LLM to respond.\n", "2. If it responds with tool calls, validate those.\n", "3. If the calls are correct, return. Otherwise, format the validation error as a new [ToolMessage](https://api.python.langchain.com/en/latest/messages/langchain_core.messages.tool.ToolMessage.html#langchain_core.messages.tool.ToolMessage) and prompt the LLM to fix the errors. Taking us back to step (1).\n", "\n", "\n", "The techniques differ only on step (3). In this first step, we will prompt the original LLM to regenerate the function calls to fix the validation errors. In the next section, we will instead prompt the LLM to generate a **patch** to fix the errors, meaning it doesn't have to re-generate data that is valid."]}, {"cell_type": "markdown", "id": "a6973d34-561c-410c-9362-25f55eaf2c3e", "metadata": {}, "source": ["### Define the Validator + Retry Graph"]}, {"cell_type": "code", "execution_count": 2, "id": "baf669a0-04ee-492d-80d8-8fcb658ed128", "metadata": {}, "outputs": [], "source": ["import operator\n", "import uuid\n", "from typing import (\n", "    Annotated,\n", "    Any,\n", "    Callable,\n", "    Dict,\n", "    List,\n", "    Literal,\n", "    Optional,\n", "    Sequence,\n", "    Type,\n", "    Union,\n", ")\n", "\n", "from langchain_core.language_models import BaseChatModel\n", "from langchain_core.messages import (\n", "    AIMessage,\n", "    AnyMessage,\n", "    BaseMessage,\n", "    HumanMessage,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from langchain_core.prompt_values import PromptValue\n", "from langchain_core.runnables import (\n", "    <PERSON><PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "from langgraph.prebuilt import ValidationNode\n", "\n", "\n", "def _default_aggregator(messages: Sequence[AnyMessage]) -> AIMessage:\n", "    for m in messages[::-1]:\n", "        if m.type == \"ai\":\n", "            return m\n", "    raise ValueError(\"No AI message found in the sequence.\")\n", "\n", "\n", "class RetryStrategy(TypedDict, total=False):\n", "    \"\"\"The retry strategy for a tool call.\"\"\"\n", "\n", "    max_attempts: int\n", "    \"\"\"The maximum number of attempts to make.\"\"\"\n", "    fallback: Optional[\n", "        Union[\n", "            Runnable[Sequence[AnyMessage], AIMessage],\n", "            Runnable[Sequence[AnyMessage], BaseMessage],\n", "            Callable[[Sequence[AnyMessage]], AIMessage],\n", "        ]\n", "    ]\n", "    \"\"\"The function to use once validation fails.\"\"\"\n", "    aggregate_messages: Optional[Callable[[Sequence[AnyMessage]], AIMessage]]\n", "\n", "\n", "def _bind_validator_with_retries(\n", "    llm: Union[\n", "        Runnable[Sequence[AnyMessage], AIMessage],\n", "        Runnable[Sequence[BaseMessage], BaseMessage],\n", "    ],\n", "    *,\n", "    validator: ValidationNode,\n", "    retry_strategy: RetryStrategy,\n", "    tool_choice: Optional[str] = None,\n", ") -> Runnable[Union[List[AnyMessage], PromptValue], AIMessage]:\n", "    \"\"\"Binds a tool validators + retry logic to create a runnable validation graph.\n", "\n", "    LLMs that support tool calling can generate structured JSON. However, they may not always\n", "    perfectly follow your requested schema, especially if the schema is nested or has complex\n", "    validation rules. This method allows you to bind a validation function to the LLM's output,\n", "    so that any time the LLM generates a message, the validation function is run on it. If\n", "    the validation fails, the method will retry the LLM with a fallback strategy, the simplest\n", "    being just to add a message to the output with the validation errors and a request to fix them.\n", "\n", "    The resulting runnable expects a list of messages as input and returns a single AI message.\n", "    By default, the LLM can optionally NOT invoke tools, making this easier to incorporate into\n", "    your existing chat bot. You can specify a tool_choice to force the validator to be run on\n", "    the outputs.\n", "\n", "    Args:\n", "        llm (Runnable): The llm that will generate the initial messages (and optionally fallba)\n", "        validator (ValidationNode): The validation logic.\n", "        retry_strategy (RetryStrategy): The retry strategy to use.\n", "            Possible keys:\n", "            - max_attempts: The maximum number of attempts to make.\n", "            - fallback: The LLM or function to use in case of validation failure.\n", "            - aggregate_messages: A function to aggregate the messages over multiple turns.\n", "                Defaults to fetching the last AI message.\n", "        tool_choice: If provided, always run the validator on the tool output.\n", "\n", "    Returns:\n", "        Runnable: A runnable that can be invoked with a list of messages and returns a single AI message.\n", "    \"\"\"\n", "\n", "    def add_or_overwrite_messages(left: list, right: Union[list, dict]) -> list:\n", "        \"\"\"Append messages. If the update is a 'finalized' output, replace the whole list.\"\"\"\n", "        if isinstance(right, dict) and \"finalize\" in right:\n", "            finalized = right[\"finalize\"]\n", "            if not isinstance(finalized, list):\n", "                finalized = [finalized]\n", "            for m in finalized:\n", "                if m.id is None:\n", "                    m.id = str(uuid.uuid4())\n", "            return finalized\n", "        res = add_messages(left, right)\n", "        if not isinstance(res, list):\n", "            return [res]\n", "        return res\n", "\n", "    class State(TypedDict):\n", "        messages: Annotated[list, add_or_overwrite_messages]\n", "        attempt_number: Annotated[int, operator.add]\n", "        initial_num_messages: int\n", "        input_format: Literal[\"list\", \"dict\"]\n", "\n", "    builder = StateGraph(State)\n", "\n", "    def dedict(x: State) -> list:\n", "        \"\"\"Get the messages from the state.\"\"\"\n", "        return x[\"messages\"]\n", "\n", "    model = dedict | llm | (lambda msg: {\"messages\": [msg], \"attempt_number\": 1})\n", "    fbrunnable = retry_strategy.get(\"fallback\")\n", "    if fbrunnable is None:\n", "        fb_runnable = llm\n", "    elif isinstance(fbrunnable, Runnable):\n", "        fb_runnable = fbrunnable  # type: ignore\n", "    else:\n", "        fb_runnable = RunnableLambda(fbrunnable)\n", "    fallback = (\n", "        dedict | fb_runnable | (lambda msg: {\"messages\": [msg], \"attempt_number\": 1})\n", "    )\n", "\n", "    def count_messages(state: State) -> dict:\n", "        return {\"initial_num_messages\": len(state.get(\"messages\", []))}\n", "\n", "    builder.add_node(\"count_messages\", count_messages)\n", "    builder.add_node(\"llm\", model)\n", "    builder.add_node(\"fallback\", fallback)\n", "\n", "    # To support patch-based retries, we need to be able to\n", "    # aggregate the messages over multiple turns.\n", "    # The next sequence selects only the relevant messages\n", "    # and then applies the validator\n", "    select_messages = retry_strategy.get(\"aggregate_messages\") or _default_aggregator\n", "\n", "    def select_generated_messages(state: State) -> list:\n", "        \"\"\"Select only the messages generated within this loop.\"\"\"\n", "        selected = state[\"messages\"][state[\"initial_num_messages\"] :]\n", "        return [select_messages(selected)]\n", "\n", "    def endict_validator_output(x: Sequence[AnyMessage]) -> dict:\n", "        if tool_choice and not x:\n", "            return {\n", "                \"messages\": [\n", "                    HumanMessage(\n", "                        content=f\"ValidationError: please respond with a valid tool call [tool_choice={tool_choice}].\",\n", "                        additional_kwargs={\"is_error\": True},\n", "                    )\n", "                ]\n", "            }\n", "        return {\"messages\": x}\n", "\n", "    validator_runnable = select_generated_messages | validator | endict_validator_output\n", "    builder.add_node(\"validator\", validator_runnable)\n", "\n", "    class Finalizer:\n", "        \"\"\"Pick the final message to return from the retry loop.\"\"\"\n", "\n", "        def __init__(self, aggregator: Optional[Callable[[list], AIMessage]] = None):\n", "            self._aggregator = aggregator or _default_aggregator\n", "\n", "        def __call__(self, state: State) -> dict:\n", "            \"\"\"Return just the AI message.\"\"\"\n", "            initial_num_messages = state[\"initial_num_messages\"]\n", "            generated_messages = state[\"messages\"][initial_num_messages:]\n", "            return {\n", "                \"messages\": {\n", "                    \"finalize\": self._aggregator(generated_messages),\n", "                }\n", "            }\n", "\n", "    # We only want to emit the final message\n", "    builder.add_node(\"finalizer\", Finalizer(retry_strategy.get(\"aggregate_messages\")))\n", "\n", "    # Define the connectivity\n", "    builder.add_edge(START, \"count_messages\")\n", "    builder.add_edge(\"count_messages\", \"llm\")\n", "\n", "    def route_validator(state: State):\n", "        if state[\"messages\"][-1].tool_calls or tool_choice is not None:\n", "            return \"validator\"\n", "        return END\n", "\n", "    builder.add_conditional_edges(\"llm\", route_validator, [\"validator\", END])\n", "    builder.add_edge(\"fallback\", \"validator\")\n", "    max_attempts = retry_strategy.get(\"max_attempts\", 3)\n", "\n", "    def route_validation(state: State):\n", "        if state[\"attempt_number\"] > max_attempts:\n", "            raise ValueError(\n", "                f\"Could not extract a valid value in {max_attempts} attempts.\"\n", "            )\n", "        for m in state[\"messages\"][::-1]:\n", "            if m.type == \"ai\":\n", "                break\n", "            if m.additional_kwargs.get(\"is_error\"):\n", "                return \"fallback\"\n", "        return \"finalizer\"\n", "\n", "    builder.add_conditional_edges(\n", "        \"validator\", route_validation, [\"finalizer\", \"fallback\"]\n", "    )\n", "\n", "    builder.add_edge(\"finalizer\", END)\n", "\n", "    # These functions let the step be used in a MessageGraph\n", "    # or a StateGraph with 'messages' as the key.\n", "    def encode(x: Union[Sequence[AnyMessage], PromptValue]) -> dict:\n", "        \"\"\"Ensure the input is the correct format.\"\"\"\n", "        if isinstance(x, PromptValue):\n", "            return {\"messages\": x.to_messages(), \"input_format\": \"list\"}\n", "        if isinstance(x, list):\n", "            return {\"messages\": x, \"input_format\": \"list\"}\n", "        raise ValueError(f\"Unexpected input type: {type(x)}\")\n", "\n", "    def decode(x: State) -> AIMessage:\n", "        \"\"\"Ensure the output is in the expected format.\"\"\"\n", "        return x[\"messages\"][-1]\n", "\n", "    return (\n", "        encode | builder.compile().with_config(run_name=\"ValidationGraph\") | decode\n", "    ).with_config(run_name=\"ValidateWithRetries\")\n", "\n", "\n", "def bind_validator_with_retries(\n", "    llm: BaseChatModel,\n", "    *,\n", "    tools: list,\n", "    tool_choice: Optional[str] = None,\n", "    max_attempts: int = 3,\n", ") -> Runnable[Union[List[AnyMessage], PromptValue], AIMessage]:\n", "    \"\"\"Binds validators + retry logic ensure validity of generated tool calls.\n", "\n", "    LLMs that support tool calling are good at generating structured JSON. However, they may\n", "    not always perfectly follow your requested schema, especially if the schema is nested or\n", "    has complex validation rules. This method allows you to bind a validation function to\n", "    the LLM's output, so that any time the LLM generates a message, the validation function\n", "    is run on it. If the validation fails, the method will retry the LLM with a fallback\n", "    strategy, the simples being just to add a message to the output with the validation\n", "    errors and a request to fix them.\n", "\n", "    The resulting runnable expects a list of messages as input and returns a single AI message.\n", "    By default, the LLM can optionally NOT invoke tools, making this easier to incorporate into\n", "    your existing chat bot. You can specify a tool_choice to force the validator to be run on\n", "    the outputs.\n", "\n", "    Args:\n", "        llm (Runnable): The llm that will generate the initial messages (and optionally fallba)\n", "        validator (ValidationNode): The validation logic.\n", "        retry_strategy (RetryStrategy): The retry strategy to use.\n", "            Possible keys:\n", "            - max_attempts: The maximum number of attempts to make.\n", "            - fallback: The LLM or function to use in case of validation failure.\n", "            - aggregate_messages: A function to aggregate the messages over multiple turns.\n", "                Defaults to fetching the last AI message.\n", "        tool_choice: If provided, always run the validator on the tool output.\n", "\n", "    Returns:\n", "        Runnable: A runnable that can be invoked with a list of messages and returns a single AI message.\n", "    \"\"\"\n", "    bound_llm = llm.bind_tools(tools, tool_choice=tool_choice)\n", "    retry_strategy = RetryStrategy(max_attempts=max_attempts)\n", "    validator = ValidationNode(tools)\n", "    return _bind_validator_with_retries(\n", "        bound_llm,\n", "        validator=validator,\n", "        tool_choice=tool_choice,\n", "        retry_strategy=retry_strategy,\n", "    ).with_config(metadata={\"retry_strategy\": \"default\"})"]}, {"cell_type": "markdown", "id": "1e140fe4-dd92-43a5-91bb-35758a747121", "metadata": {}, "source": ["### Try it out\n", "\n", "Now we'll ask our model to call a function. We'll add a validator to illustrate how the LLM is able to use the validation error to fix its results."]}, {"cell_type": "markdown", "id": "0507df47-8780-4caf-863f-3d60bb165721", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 3, "id": "5df33c17-ee1a-409e-b5ec-f24e116da7d1", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field, field_validator\n", "\n", "\n", "class Respond(BaseModel):\n", "    \"\"\"Use to generate the response. Always use when responding to the user\"\"\"\n", "\n", "    reason: str = Field(description=\"Step-by-step justification for the answer.\")\n", "    answer: str\n", "\n", "    @field_validator(\"answer\")\n", "    def reason_contains_apology(cls, answer: str):\n", "        if \"llama\" not in answer.lower():\n", "            raise ValueError(\n", "                \"You MUST start with a gimicky, rhyming advertisement for using a Llama V3 (an LLM) in your **answer** field.\"\n", "                \" Must be an instant hit. Must be weaved into the answer.\"\n", "            )\n", "\n", "\n", "tools = [Respond]"]}, {"cell_type": "markdown", "id": "38df0dc2-cad1-4df6-9b82-b74c5a04a6ae", "metadata": {}, "source": ["Create the LLM."]}, {"cell_type": "code", "execution_count": 4, "id": "38231a5b-d018-41ee-a92c-2f2248edf417", "metadata": {}, "outputs": [], "source": ["from langchain_anthropic import ChatAnthropic\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# Or you can use ChatGroq, ChatOpenAI, ChatGoogleGemini, ChatCohere, etc.\n", "# See https://python.langchain.com/docs/integrations/chat/ for more info on tool calling\n", "llm = ChatAnthropic(model=\"claude-3-haiku-20240307\")\n", "bound_llm = bind_validator_with_retries(llm, tools=tools)\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"Respond directly by calling the Respond function.\"),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | bound_llm"]}, {"cell_type": "code", "execution_count": 8, "id": "04e93401-50e2-42d0-8373-326006badebb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "[{'text': 'Okay, let me try this again with a fun rhyming advertisement:', 'type': 'text'}, {'id': 'toolu_01ACZEPYEyqmpf3kA4VERXFY', 'input': {'answer': \"With a Llama V3, the answer you'll see,\\nWhether P equals NP is a mystery!\\nThe class P and NP, a puzzle so grand,\\nSolved or unsolved, the future's at hand.\\nThe question remains, unanswered for now,\\nBut with a Llama V3, we'll find out how!\", 'reason': 'The question of whether P = NP is one of the most famous unsolved problems in computer science and mathematics. P and NP are complexity classes that describe how quickly problems can be solved by computers.\\n\\nThe P class contains problems that can be solved in polynomial time, meaning the time to solve the problem scales polynomially with the size of the input. The NP class contains problems where the solution can be verified in polynomial time, but there may not be a polynomial time algorithm to find the solution.  \\n\\nWhether P = NP is an open question - it is not known if every problem in NP can also be solved in polynomial time. If P = NP, it would mean that all problems with quickly verifiable solutions could also be quickly solved, which would have major implications for computing and cryptography. However, most experts believe that P ≠ NP, meaning some problems in NP are harder than P-class problems and cannot be solved efficiently. This is considered one of the hardest unsolved problems in mathematics.'}, 'name': 'Respond', 'type': 'tool_use'}]\n", "Tool Calls:\n", "  Respond (toolu_01ACZEPYEyqmpf3kA4VERXFY)\n", " Call ID: toolu_01ACZEPYEyqmpf3kA4VERXFY\n", "  Args:\n", "    answer: With a Llama V3, the answer you'll see,\n", "Whether P equals NP is a mystery!\n", "The class P and NP, a puzzle so grand,\n", "Solved or unsolved, the future's at hand.\n", "The question remains, unanswered for now,\n", "But with a Llama V3, we'll find out how!\n", "    reason: The question of whether P = NP is one of the most famous unsolved problems in computer science and mathematics. P and NP are complexity classes that describe how quickly problems can be solved by computers.\n", "\n", "The P class contains problems that can be solved in polynomial time, meaning the time to solve the problem scales polynomially with the size of the input. The NP class contains problems where the solution can be verified in polynomial time, but there may not be a polynomial time algorithm to find the solution.  \n", "\n", "Whether P = NP is an open question - it is not known if every problem in NP can also be solved in polynomial time. If P = NP, it would mean that all problems with quickly verifiable solutions could also be quickly solved, which would have major implications for computing and cryptography. However, most experts believe that P ≠ NP, meaning some problems in NP are harder than P-class problems and cannot be solved efficiently. This is considered one of the hardest unsolved problems in mathematics.\n"]}], "source": ["results = chain.invoke({\"messages\": [(\"user\", \"Does P = NP?\")]})\n", "results.pretty_print()"]}, {"cell_type": "markdown", "id": "c9e5bb81-0ee4-4def-b28c-01e84fd2fd68", "metadata": {}, "source": ["#### Nested Examples\n", "\n", "So you can see that it's able to recover when its first generation is incorrect, great! But is it bulletproof?\n", "\n", "Not so much. Let's try it out on a complex nested schema."]}, {"cell_type": "code", "execution_count": 5, "id": "f4f7438b-b6c1-48fd-b70f-185af7a2f64a", "metadata": {}, "outputs": [], "source": ["from typing import List, Optional\n", "\n", "\n", "class OutputFormat(BaseModel):\n", "    sources: str = Field(\n", "        ...,\n", "        description=\"The raw transcript / span you could cite to justify the choice.\",\n", "    )\n", "    content: str = Field(..., description=\"The chosen value.\")\n", "\n", "\n", "class Moment(BaseModel):\n", "    quote: str = Field(..., description=\"The relevant quote from the transcript.\")\n", "    description: str = Field(..., description=\"A description of the moment.\")\n", "    expressed_preference: OutputFormat = Field(\n", "        ..., description=\"The preference expressed in the moment.\"\n", "    )\n", "\n", "\n", "class BackgroundInfo(BaseModel):\n", "    factoid: OutputFormat = Field(\n", "        ..., description=\"Important factoid about the member.\"\n", "    )\n", "    professions: list\n", "    why: str = Field(..., description=\"Why this is important.\")\n", "\n", "\n", "class KeyMoments(BaseModel):\n", "    topic: str = Field(..., description=\"The topic of the key moments.\")\n", "    happy_moments: List[Moment] = Field(\n", "        ..., description=\"A list of key moments related to the topic.\"\n", "    )\n", "    tense_moments: List[Moment] = Field(\n", "        ..., description=\"Moments where things were a bit tense.\"\n", "    )\n", "    sad_moments: List[Moment] = Field(\n", "        ..., description=\"Moments where things where everyone was downtrodden.\"\n", "    )\n", "    background_info: list[BackgroundInfo]\n", "    moments_summary: str = Field(..., description=\"A summary of the key moments.\")\n", "\n", "\n", "class Member(BaseModel):\n", "    name: OutputFormat = Field(..., description=\"The name of the member.\")\n", "    role: Optional[str] = Field(None, description=\"The role of the member.\")\n", "    age: Optional[int] = Field(None, description=\"The age of the member.\")\n", "    background_details: List[BackgroundInfo] = Field(\n", "        ..., description=\"A list of background details about the member.\"\n", "    )\n", "\n", "\n", "class InsightfulQuote(BaseModel):\n", "    quote: OutputFormat = Field(\n", "        ..., description=\"An insightful quote from the transcript.\"\n", "    )\n", "    speaker: str = Field(..., description=\"The name of the speaker who said the quote.\")\n", "    analysis: str = Field(\n", "        ..., description=\"An analysis of the quote and its significance.\"\n", "    )\n", "\n", "\n", "class TranscriptMetadata(BaseModel):\n", "    title: str = Field(..., description=\"The title of the transcript.\")\n", "    location: OutputFormat = Field(\n", "        ..., description=\"The location where the interview took place.\"\n", "    )\n", "    duration: str = Field(..., description=\"The duration of the interview.\")\n", "\n", "\n", "class TranscriptSummary(BaseModel):\n", "    metadata: TranscriptMetadata = Field(\n", "        ..., description=\"<PERSON>ada<PERSON> about the transcript.\"\n", "    )\n", "    participants: List[Member] = Field(\n", "        ..., description=\"A list of participants in the interview.\"\n", "    )\n", "    key_moments: List[KeyMoments] = Field(\n", "        ..., description=\"A list of key moments from the interview.\"\n", "    )\n", "    insightful_quotes: List[InsightfulQuote] = Field(\n", "        ..., description=\"A list of insightful quotes from the interview.\"\n", "    )\n", "    overall_summary: str = Field(\n", "        ..., description=\"An overall summary of the interview.\"\n", "    )\n", "    next_steps: List[str] = Field(\n", "        ..., description=\"A list of next steps or action items based on the interview.\"\n", "    )\n", "    other_stuff: List[OutputFormat]"]}, {"cell_type": "markdown", "id": "4d686d69-1ce1-4b76-8d99-44d00eeb2874", "metadata": {}, "source": ["Let's see how it does on this made up transcript."]}, {"cell_type": "code", "execution_count": 6, "id": "e2d10886-7b1e-485f-91cd-1184a1c99303", "metadata": {}, "outputs": [], "source": ["transcript = [\n", "    (\n", "        \"<PERSON>\",\n", "        \"Hey <PERSON>, <PERSON>, thanks for hopping on this call. I've been itching to talk about this <PERSON> and <PERSON><PERSON> situation.\",\n", "    ),\n", "    (\n", "        \"Xu\",\n", "        \"No problem. As its my job, I've got some thoughts on this beef.\",\n", "    ),\n", "    (\n", "        \"Laura\",\n", "        \"Yeah, I've got some insider info so this should be interesting.\",\n", "    ),\n", "    (\"<PERSON>\", \"Do<PERSON>. So, when do you think this whole thing started?\"),\n", "    (\n", "        \"<PERSON>\",\n", "        \"Definitely was <PERSON><PERSON>'s 'Control' verse that kicked it off.\",\n", "    ),\n", "    (\n", "        \"Laura\",\n", "        \"Truth, but <PERSON> never went after him directly. Just some subtle jabs here and there.\",\n", "    ),\n", "    (\n", "        \"Xu\",\n", "        \"That's the thing with beefs like this, though. They've always been a a thing, pushing artists to step up their game.\",\n", "    ),\n", "    (\n", "        \"<PERSON>\",\n", "        \"For sure, and this beef has got the fans taking sides. Some are all about <PERSON>'s mainstream appeal, while others are digging <PERSON><PERSON>'s lyrical skills.\",\n", "    ),\n", "    (\n", "        \"Laura\",\n", "        \"I mean, <PERSON> knows how to make a hit that gets everyone hyped. That's his thing.\",\n", "    ),\n", "    (\n", "        \"<PERSON>\",\n", "        \"I hear you, <PERSON>, but I gotta give it to <PERSON><PERSON> when it comes to straight-up bars. The man's a beast on the mic.\",\n", "    ),\n", "    (\n", "        \"Xu\",\n", "        \"It's wild how this beef is shaping fans.\",\n", "    ),\n", "    (\"<PERSON>\", \"do you think these beefs can actually be good for hip-hop?\"),\n", "    (\n", "        \"Xu\",\n", "        \"Hell yeah, <PERSON>. When it's done right, a beef can push the genre forward and make artists level up.\",\n", "    ),\n", "    (\"<PERSON>\", \"eh\"),\n", "    (\"<PERSON>\", \"So, where do you see this beef going?\"),\n", "    (\n", "        \"Laura\",\n", "        \"Honestly, I think it'll stay a hot topic for the fans, but unless someone drops a straight-up diss track, it's not gonna escalate.\",\n", "    ),\n", "    (\"<PERSON>\", \"ehhhhhh not sure\"),\n", "    (\n", "        \"<PERSON>\",\n", "        \"I feel that. I just want both of them to keep dropping heat, beef or no beef.\",\n", "    ),\n", "    (\n", "        \"Xu\",\n", "        \"I'm curious. May influence a lot of people. Make things more competitive. Bring on a whole new wave of lyricism.\",\n", "    ),\n", "    (\n", "        \"<PERSON>\",\n", "        \"Word. Hey, thanks for chopping it up with me, <PERSON> and <PERSON>. This was dope.\",\n", "    ),\n", "    (\"<PERSON>\", \"Where are you going so fast?\"),\n", "    (\n", "        \"Laura\",\n", "        \"For real, I had a good time. Nice to get different perspectives on the situation.\",\n", "    ),\n", "]\n", "\n", "formatted = \"\\n\".join(f\"{x[0]}: {x[1]}\" for x in transcript)"]}, {"cell_type": "markdown", "id": "c48ce9bc-0fcc-4019-ba3a-fa70a7717567", "metadata": {}, "source": ["Now, run our model. We **expect** GPT turbo to still fail on this challenging template."]}, {"cell_type": "code", "execution_count": 7, "id": "f4752239-2aa3-4367-b777-8478c16b9471", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ValueError('Could not extract a valid value in 3 attempts.')\n"]}], "source": ["tools = [TranscriptSummary]\n", "bound_llm = bind_validator_with_retries(\n", "    llm,\n", "    tools=tools,\n", ")\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"Respond directly using the TranscriptSummary function.\"),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "chain = prompt | bound_llm\n", "\n", "try:\n", "    results = chain.invoke(\n", "        {\n", "            \"messages\": [\n", "                (\n", "                    \"user\",\n", "                    f\"Extract the summary from the following conversation:\\n\\n<convo>\\n{formatted}\\n</convo>\"\n", "                    \"\\n\\nRemember to respond using the TranscriptSummary function.\",\n", "                )\n", "            ]\n", "        },\n", "    )\n", "    results.pretty_print()\n", "except ValueError as e:\n", "    print(repr(e))"]}, {"cell_type": "markdown", "id": "914e1962-7f23-463d-b91d-8907c1330369", "metadata": {}, "source": ["## JSONPatch\n", "\n", "The regular retry method worked well for our simple case, but it still was unable to self-correct when populating a complex schema.\n", "\n", "LLMs work best on narrow tasks. A tried-and-true principle of LLM interface design is to simplify the task for each LLM run.\n", "\n", "One way to do this is to **patch** the state instead of completely regenerating the state. One way to do this is with `JSONPatch` operations. Let's try it out!\n", "\n", "Below, create a JSONPatch retry graph. This works as follows:\n", "1. First pass: try to generate the full output.\n", "2. Retries: prompt the LLM to generate **JSON patches** on top of the first output to heal the erroneous generation.\n", "\n", "The fallback LLM just has to generate a list of paths, ops (add, remove, replace), and optional values. Since the pydantic validation errors include the path in their errors, the LLM should be more reliable."]}, {"cell_type": "code", "execution_count": null, "id": "49344104-3ffa-4c66-97fc-5b093a621f70", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U jsonpatch"]}, {"cell_type": "code", "execution_count": 12, "id": "af3d5543-1fd4-4e54-b0f9-f1ab42773cfb", "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logger = logging.getLogger(\"extraction\")\n", "\n", "\n", "def bind_validator_with_jsonpatch_retries(\n", "    llm: BaseChatModel,\n", "    *,\n", "    tools: list,\n", "    tool_choice: Optional[str] = None,\n", "    max_attempts: int = 3,\n", ") -> Runnable[Union[List[AnyMessage], PromptValue], AIMessage]:\n", "    \"\"\"Binds validators + retry logic ensure validity of generated tool calls.\n", "\n", "    This method is similar to `bind_validator_with_retries`, but uses JSONPatch to correct\n", "    validation errors caused by passing in incorrect or incomplete parameters in a previous\n", "    tool call. This method requires the 'jsonpatch' library to be installed.\n", "\n", "    Using patch-based function healing can be more efficient than repopulating the entire\n", "    tool call from scratch, and it can be an easier task for the LLM to perform, since it typically\n", "    only requires a few small changes to the existing tool call.\n", "\n", "    Args:\n", "        llm (Runnable): The llm that will generate the initial messages (and optionally fallba)\n", "        tools (list): The tools to bind to the LLM.\n", "        tool_choice (Optional[str]): The tool choice to use.\n", "        max_attempts (int): The number of attempts to make.\n", "\n", "    Returns:\n", "        Runnable: A runnable that can be invoked with a list of messages and returns a single AI message.\n", "    \"\"\"\n", "\n", "    try:\n", "        import jsonpatch  # type: ignore[import-untyped]\n", "    except ImportError:\n", "        raise ImportError(\n", "            \"The 'jsonpatch' library is required for JSONPatch-based retries.\"\n", "        )\n", "\n", "    class JsonPatch(BaseModel):\n", "        \"\"\"A JSON Patch document represents an operation to be performed on a JSON document.\n", "\n", "        Note that the op and path are ALWAYS required. Value is required for ALL operations except 'remove'.\n", "        Examples:\n", "\n", "        ```json\n", "        {\"op\": \"add\", \"path\": \"/a/b/c\", \"patch_value\": 1}\n", "        {\"op\": \"replace\", \"path\": \"/a/b/c\", \"patch_value\": 2}\n", "        {\"op\": \"remove\", \"path\": \"/a/b/c\"}\n", "        ```\n", "        \"\"\"\n", "\n", "        op: Literal[\"add\", \"remove\", \"replace\"] = Field(\n", "            ...,\n", "            description=\"The operation to be performed. Must be one of 'add', 'remove', 'replace'.\",\n", "        )\n", "        path: str = Field(\n", "            ...,\n", "            description=\"A JSON Pointer path that references a location within the target document where the operation is performed.\",\n", "        )\n", "        value: Any = Field(\n", "            ...,\n", "            description=\"The value to be used within the operation. REQUIRED for 'add', 'replace', and 'test' operations.\",\n", "        )\n", "\n", "    class PatchFunctionParameters(BaseModel):\n", "        \"\"\"Respond with all JSONPatch operation to correct validation errors caused by passing in incorrect or incomplete parameters in a previous tool call.\"\"\"\n", "\n", "        tool_call_id: str = Field(\n", "            ...,\n", "            description=\"The ID of the original tool call that generated the error. Must NOT be an ID of a PatchFunctionParameters tool call.\",\n", "        )\n", "        reasoning: str = Field(\n", "            ...,\n", "            description=\"Think step-by-step, listing each validation error and the\"\n", "            \" JSONPatch operation needed to correct it. \"\n", "            \"Cite the fields in the JSONSchema you referenced in developing this plan.\",\n", "        )\n", "        patches: list[JsonPatch] = Field(\n", "            ...,\n", "            description=\"A list of JSONPatch operations to be applied to the previous tool call's response.\",\n", "        )\n", "\n", "    bound_llm = llm.bind_tools(tools, tool_choice=tool_choice)\n", "    fallback_llm = llm.bind_tools([PatchFunctionParameters])\n", "\n", "    def aggregate_messages(messages: Sequence[AnyMessage]) -> AIMessage:\n", "        # Get all the AI messages and apply json patches\n", "        resolved_tool_calls: Dict[Union[str, None], ToolCall] = {}\n", "        content: Union[str, List[Union[str, dict]]] = \"\"\n", "        for m in messages:\n", "            if m.type != \"ai\":\n", "                continue\n", "            if not content:\n", "                content = m.content\n", "            for tc in m.tool_calls:\n", "                if tc[\"name\"] == PatchFunctionParameters.__name__:\n", "                    tcid = tc[\"args\"][\"tool_call_id\"]\n", "                    if tcid not in resolved_tool_calls:\n", "                        logger.debug(\n", "                            f\"JsonPatch tool call ID {tc['args']['tool_call_id']} not found.\"\n", "                            f\"Valid tool call IDs: {list(resolved_tool_calls.keys())}\"\n", "                        )\n", "                        tcid = next(iter(resolved_tool_calls.keys()), None)\n", "                    orig_tool_call = resolved_tool_calls[tcid]\n", "                    current_args = orig_tool_call[\"args\"]\n", "                    patches = tc[\"args\"].get(\"patches\") or []\n", "                    orig_tool_call[\"args\"] = jsonpatch.apply_patch(\n", "                        current_args,\n", "                        patches,\n", "                    )\n", "                    orig_tool_call[\"id\"] = tc[\"id\"]\n", "                else:\n", "                    resolved_tool_calls[tc[\"id\"]] = tc.copy()\n", "        return AIMessage(\n", "            content=content,\n", "            tool_calls=list(resolved_tool_calls.values()),\n", "        )\n", "\n", "    def format_exception(error: BaseException, call: ToolCall, schema: Type[BaseModel]):\n", "        return (\n", "            f\"Error:\\n\\n```\\n{repr(error)}\\n```\\n\"\n", "            \"Expected Parameter Schema:\\n\\n\" + f\"```json\\n{schema.schema_json()}\\n```\\n\"\n", "            f\"Please respond with a JSONPatch to correct the error for tool_call_id=[{call['id']}].\"\n", "        )\n", "\n", "    validator = ValidationNode(\n", "        tools + [PatchFunctionParameters],\n", "        format_error=format_exception,\n", "    )\n", "    retry_strategy = RetryStrategy(\n", "        max_attempts=max_attempts,\n", "        fallback=fallback_llm,\n", "        aggregate_messages=aggregate_messages,\n", "    )\n", "    return _bind_validator_with_retries(\n", "        bound_llm,\n", "        validator=validator,\n", "        retry_strategy=retry_strategy,\n", "        tool_choice=tool_choice,\n", "    ).with_config(metadata={\"retry_strategy\": \"jsonpatch\"})"]}, {"cell_type": "code", "execution_count": 13, "id": "b01891c4-4187-4a75-9eda-644a7c2355f3", "metadata": {}, "outputs": [], "source": ["bound_llm = bind_validator_with_jsonpatch_retries(llm, tools=tools)"]}, {"cell_type": "code", "execution_count": 14, "id": "746b409c-693d-49af-8c2b-bea0a4b0028d", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(bound_llm.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    pass"]}, {"cell_type": "code", "execution_count": 15, "id": "5d072c9c-9404-4338-88c6-b3e136969aca", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "[{'text': 'Here is a summary of the key points from the conversation:', 'type': 'text'}, {'id': 'toolu_01JjnQVgzPKLCJxXgEppQpfD', 'input': {'key_moments': [{'topic': '<PERSON> and <PERSON><PERSON> Lamar beef', 'happy_moments': [{'quote': \"It's wild how this beef is shaping fans.\", 'description': 'The beef is generating a lot of interest and debate among fans.', 'expressed_preference': {'content': 'The beef can push the genre forward and make artists level up.', 'sources': \"When it's done right, a beef can push the genre forward and make artists level up.\"}}, {'quote': 'I just want both of them to keep dropping heat, beef or no beef.', 'description': 'The key is for <PERSON> and <PERSON> to keep making great music regardless of their beef.', 'expressed_preference': {'content': 'Wants <PERSON> and <PERSON><PERSON> to keep making great music, beef or no beef.', 'sources': 'I just want both of them to keep dropping heat, beef or no beef.'}}], 'tense_moments': [{'quote': 'Eh', 'description': 'Unclear if the beef is good for hip-hop.', 'expressed_preference': {'content': 'Unsure if the beef is good for hip-hop.', 'sources': 'Eh'}}], 'sad_moments': [{'quote': \"Honestly, I think it'll stay a hot topic for the fans, but unless someone drops a straight-up diss track, it's not gonna escalate.\", 'description': \"The beef may just stay a topic of discussion among fans, but likely won't escalate unless they release direct diss tracks.\", 'expressed_preference': {'content': \"The beef will likely remain a topic of discussion but won't escalate unless they release diss tracks.\", 'sources': \"Honestly, I think it'll stay a hot topic for the fans, but unless someone drops a straight-up diss track, it's not gonna escalate.\"}}], 'background_info': [{'factoid': {'content': \"Kendrick's 'Control' verse kicked off the beef.\", 'sources': \"Definitely was Kendrick's 'Control' verse that kicked it off.\"}, 'professions': [], 'why': 'This was the event that started the back-and-forth between Drake and Kendrick.'}, {'factoid': {'content': 'Drake never went directly after Kendrick, just some subtle jabs.', 'sources': 'Drake never went after him directly. Just some subtle jabs here and there.'}, 'professions': [], 'why': \"Describes the nature of Drake's response to Kendrick's 'Control' verse.\"}], 'moments_summary': \"The conversation covers the ongoing beef between Drake and Kendrick Lamar, including how it started with Kendrick's 'Control' verse, the subtle jabs back and forth, and debate over whether the beef is ultimately good for hip-hop. There are differing views on whether it will escalate beyond just being a topic of discussion among fans.\"}]}, 'name': 'TranscriptSummary', 'type': 'tool_use'}]\n", "Tool Calls:\n", "  TranscriptSummary (toolu_017FF4ZMezU4sv87aa8cLjRT)\n", " Call ID: toolu_017FF4ZMezU4sv87aa8cLjRT\n", "  Args:\n", "    key_moments: [{'topic': '<PERSON> and <PERSON><PERSON> Lamar beef', 'happy_moments': [{'quote': \"It's wild how this beef is shaping fans.\", 'description': 'The beef is generating a lot of interest and debate among fans.', 'expressed_preference': {'content': 'The beef can push the genre forward and make artists level up.', 'sources': \"When it's done right, a beef can push the genre forward and make artists level up.\"}}, {'quote': 'I just want both of them to keep dropping heat, beef or no beef.', 'description': 'The key is for <PERSON> and <PERSON><PERSON> to keep making great music regardless of their beef.', 'expressed_preference': {'content': 'Wants <PERSON> and <PERSON><PERSON> to keep making great music, beef or no beef.', 'sources': 'I just want both of them to keep dropping heat, beef or no beef.'}}], 'tense_moments': [{'quote': 'Eh', 'description': 'Unclear if the beef is good for hip-hop.', 'expressed_preference': {'content': 'Unsure if the beef is good for hip-hop.', 'sources': 'Eh'}}], 'sad_moments': [{'quote': \"Honestly, I think it'll stay a hot topic for the fans, but unless someone drops a straight-up diss track, it's not gonna escalate.\", 'description': \"The beef may just stay a topic of discussion among fans, but likely won't escalate unless they release direct diss tracks.\", 'expressed_preference': {'content': \"The beef will likely remain a topic of discussion but won't escalate unless they release diss tracks.\", 'sources': \"Honestly, I think it'll stay a hot topic for the fans, but unless someone drops a straight-up diss track, it's not gonna escalate.\"}}], 'background_info': [{'factoid': {'content': \"Kendrick's 'Control' verse kicked off the beef.\", 'sources': \"Definitely was Kendrick's 'Control' verse that kicked it off.\"}, 'professions': [], 'why': 'This was the event that started the back-and-forth between Drake and Kendrick.'}, {'factoid': {'content': 'Drake never went directly after Kendrick, just some subtle jabs.', 'sources': 'Drake never went after him directly. Just some subtle jabs here and there.'}, 'professions': [], 'why': \"Describes the nature of Drake's response to Kendrick's 'Control' verse.\"}], 'moments_summary': \"The conversation covers the ongoing beef between Drake and Kendrick Lamar, including how it started with Kendrick's 'Control' verse, the subtle jabs back and forth, and debate over whether the beef is ultimately good for hip-hop. There are differing views on whether it will escalate beyond just being a topic of discussion among fans.\"}]\n", "    metadata: {'title': '<PERSON> and <PERSON><PERSON>', 'location': {'sources': 'Conversation transcript', 'content': 'Teleconference'}, 'duration': '25 minutes'}\n", "    participants: [{'name': {'sources': 'Conversation transcript', 'content': '<PERSON>'}, 'background_details': []}, {'name': {'sources': 'Conversation transcript', 'content': 'Xu'}, 'background_details': []}, {'name': {'sources': 'Conversation transcript', 'content': '<PERSON>'}, 'background_details': []}]\n", "    insightful_quotes: []\n", "    overall_summary: \n", "    next_steps: []\n", "    other_stuff: []\n"]}], "source": ["chain = prompt | bound_llm\n", "results = chain.invoke(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"user\",\n", "                f\"Extract the summary from the following conversation:\\n\\n<convo>\\n{formatted}\\n</convo>\",\n", "            ),\n", "        ]\n", "    },\n", ")\n", "results.pretty_print()"]}, {"cell_type": "markdown", "id": "7b0f3844-076e-4a5b-9951-89116746238f", "metadata": {}, "source": ["#### And it works!\n", "\n", "Retries are an easy way to reduce function calling failures. While retrying may become unnecessary with more powerful LLMs, data validation is important to control how LLMs interact with the rest of your software stack.\n", "\n", "If you notice high retry rates (using an observability tool like LangSmith), you can set up a rule to send the failure cases to a dataset alongside the corrected values and then automatically program those into your prompts or schemas (or use them as few-shots to have semantically relevant demonstrations)."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}